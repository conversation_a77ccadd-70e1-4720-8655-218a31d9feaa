import { useMutation, useQuery } from '@tanstack/react-query';

import type { Conversation, Message } from '@willow/chat';
import { apiClient } from '@willow/utils/api/client';
import { useInvalidatedQuery } from '@willow/utils/react-query';

// Types for admin chat
export type AdminConversationFilter = 'myInbox' | 'all' | 'unassigned' | 'closed';

export interface AdminConversation {
  id: string;
  patientId: string;
  status: string;
  assignedAdminId: string | null;
  lastMessageText: string | null;
  createdAt: string;
  updatedAt: string;
  closedAt: string | null;
  patient: {
    id: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
  };
  assignedAdmin: {
    id: string;
    firstName: string;
    lastName: string;
  } | null;
  lastMessage: {
    id: string;
    content: string;
    createdAt: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      type: string;
    } | null;
  } | null;
  unreadMessages: number;
}

export interface AdminConversationsResponse {
  conversation: AdminConversation[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface SendAdminMessageParams {
  conversationId: string;
  content: string;
  contentType: 'text' | 'image';
  role: 'Admin' | 'Patient';
  needsReply?: boolean;
}

interface UploadAdminFileParams {
  conversationId: string;
  filename: string;
  file: Blob;
}

interface CreateAdminPatientConversationParams {
  patientId: string;
}

// Hook to get admin-patient conversations
export function useAdminPatientConversations(
  filter: AdminConversationFilter = 'all',
  page: number = 1,
  limit: number = 20,
) {
  return useQuery<AdminConversationsResponse>({
    queryKey: ['admin-patient-conversations', filter, page, limit],
    queryFn: async () => {
      const response = await apiClient.get<AdminConversationsResponse>(
        '/admin-patient-chat/conversations',
        {
          params: { filter, page, limit },
        },
      );
      return response.data;
    },
  });
}

// Hook to get a specific admin-patient conversation with messages
export function useAdminPatientConversation(conversationId: string) {
  return useQuery<Conversation>({
    queryKey: ['admin-patient-conversation', conversationId],
    queryFn: async () => {
      const response = await apiClient.get<Conversation>(
        `/admin-patient-chat/conversations/${conversationId}/messages`,
      );
      return response.data;
    },
    enabled: !!conversationId,
  });
}

// Hook to send message in admin-patient conversation
export function useSendAdminPatientMessage() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (params: SendAdminMessageParams) => {
      const response = await apiClient.post<Message>(
        '/admin-patient-chat/conversations/messages',
        {
          conversationId: params.conversationId,
          content: params.content,
          contentType: params.contentType,
          role: params.role,
          type: 'message',
          needsReply: params.needsReply || false,
        },
      );
      return response.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate conversation messages
      void invalidatedQuery(['admin-patient-conversation', variables.conversationId]);
      // Invalidate conversations list
      void invalidatedQuery(['admin-patient-conversations']);
    },
  });
}

// Hook to upload file in admin-patient conversation
export function useUploadAdminPatientFile() {
  return useMutation({
    mutationFn: async (params: UploadAdminFileParams) => {
      interface PreSignedUrlResponse {
        key: string;
        PUTPreSignedURL: string;
      }

      // Get pre-signed URL
      const preSignedUrl = await apiClient
        .get<PreSignedUrlResponse>(
          `/admin-patient-chat/conversations/${params.conversationId}/get-upload-url/${params.filename}`,
        )
        .then((res) => res.data);

      // Upload file
      await apiClient.put(preSignedUrl.PUTPreSignedURL, params.file, {
        bypassInterceptor: true,
        headers: {
          'Content-Type': params.file.type,
        },
      });

      return preSignedUrl.key;
    },
  });
}

// Hook to create admin-patient conversation
export function useCreateAdminPatientConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (params: CreateAdminPatientConversationParams) => {
      const response = await apiClient.post<{ conversationId: string }>(
        '/admin-patient-chat/conversations',
        params,
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate conversations list
      void invalidatedQuery(['admin-patient-conversations']);
    },
  });
}

// Hook to mark conversation as read
export function useMarkAdminPatientConversationAsRead() {
  return useMutation({
    mutationFn: async (conversationId: string) => {
      await apiClient.post(`/admin-patient-chat/conversations/${conversationId}/read`);
    },
  });
}

// Hook to assign conversation to admin
export function useAssignAdminPatientConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async ({
      conversationId,
      adminUserId,
    }: {
      conversationId: string;
      adminUserId?: string;
    }) => {
      const response = await apiClient.put(
        `/admin-patient-chat/conversations/${conversationId}/assign`,
        { adminUserId },
      );
      return response.data;
    },
    onSuccess: () => {
      void invalidatedQuery(['admin-patient-conversations']);
    },
  });
}

// Hook to unassign conversation
export function useUnassignAdminPatientConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      const response = await apiClient.put(
        `/admin-patient-chat/conversations/${conversationId}/unassign`,
      );
      return response.data;
    },
    onSuccess: () => {
      void invalidatedQuery(['admin-patient-conversations']);
    },
  });
}

// Hook to close conversation
export function useCloseAdminPatientConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      const response = await apiClient.put(
        `/admin-patient-chat/conversations/${conversationId}/close`,
      );
      return response.data;
    },
    onSuccess: () => {
      void invalidatedQuery(['admin-patient-conversations']);
    },
  });
}
