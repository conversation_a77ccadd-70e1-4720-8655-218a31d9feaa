import { useMutation, useQuery } from '@tanstack/react-query';

import type { Conversation, Message } from '@willow/chat';
import { apiClient } from '@willow/utils/api/client';
import { useInvalidatedQuery } from '@willow/utils/react-query';

// Types for admin-doctor chat
export type AdminConversationFilter = 'myInbox' | 'all' | 'unassigned' | 'closed';

export interface AdminDoctorConversation {
  id: string;
  patientId: string;
  status: string;
  assignedAdminId: string | null;
  lastMessageText: string | null;
  createdAt: string;
  updatedAt: string;
  closedAt: string | null;
  patient: {
    id: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    doctor: {
      id: string;
      user: {
        id: string;
        firstName: string;
        lastName: string;
      };
    };
  };
  assignedAdmin: {
    id: string;
    firstName: string;
    lastName: string;
  } | null;
  lastMessage: {
    id: string;
    content: string;
    createdAt: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      type: string;
    } | null;
  } | null;
  unreadMessages: number;
}

export interface AdminDoctorConversationsResponse {
  conversation: AdminDoctorConversation[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface SendAdminDoctorMessageParams {
  conversationId: string;
  content: string;
  contentType: 'text' | 'image';
  role: 'Admin' | 'Doctor';
  needsReply?: boolean;
}

interface UploadAdminDoctorFileParams {
  conversationId: string;
  filename: string;
  file: Blob;
}

interface CreateAdminDoctorConversationParams {
  patientId: string;
  doctorUserId: string;
}

// Hook to get admin-doctor conversations
export function useAdminDoctorConversations(
  filter: AdminConversationFilter = 'all',
  page: number = 1,
  limit: number = 20,
) {
  return useQuery<AdminDoctorConversationsResponse>({
    queryKey: ['admin-doctor-conversations', filter, page, limit],
    queryFn: async () => {
      const response = await apiClient.get<AdminDoctorConversationsResponse>(
        '/admin-doctor-chat/conversations',
        {
          params: { filter, page, limit },
        },
      );
      return response.data;
    },
  });
}

// Hook to get a specific admin-doctor conversation with messages
export function useAdminDoctorConversation(conversationId: string) {
  return useQuery<Conversation>({
    queryKey: ['admin-doctor-conversation', conversationId],
    queryFn: async () => {
      const response = await apiClient.get<Conversation>(
        `/admin-doctor-chat/conversations/${conversationId}/messages`,
      );
      return response.data;
    },
    enabled: !!conversationId,
  });
}

// Hook to send message in admin-doctor conversation
export function useSendAdminDoctorMessage() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (params: SendAdminDoctorMessageParams) => {
      const response = await apiClient.post<Message>(
        '/admin-doctor-chat/conversations/messages',
        {
          conversationId: params.conversationId,
          content: params.content,
          contentType: params.contentType,
          role: params.role,
          type: 'message',
          needsReply: params.needsReply || false,
        },
      );
      return response.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate conversation messages
      void invalidatedQuery(['admin-doctor-conversation', variables.conversationId]);
      // Invalidate conversations list
      void invalidatedQuery(['admin-doctor-conversations']);
    },
  });
}

// Hook to upload file in admin-doctor conversation
export function useUploadAdminDoctorFile() {
  return useMutation({
    mutationFn: async (params: UploadAdminDoctorFileParams) => {
      interface PreSignedUrlResponse {
        key: string;
        PUTPreSignedURL: string;
      }

      // Get pre-signed URL
      const preSignedUrl = await apiClient
        .get<PreSignedUrlResponse>(
          `/admin-doctor-chat/conversations/${params.conversationId}/get-upload-url/${params.filename}`,
        )
        .then((res) => res.data);

      // Upload file
      await apiClient.put(preSignedUrl.PUTPreSignedURL, params.file, {
        bypassInterceptor: true,
        headers: {
          'Content-Type': params.file.type,
        },
      });

      return preSignedUrl.key;
    },
  });
}

// Hook to create admin-doctor conversation
export function useCreateAdminDoctorConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (params: CreateAdminDoctorConversationParams) => {
      const response = await apiClient.post<{ conversationId: string }>(
        '/admin-doctor-chat/conversations',
        params,
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate conversations list
      void invalidatedQuery(['admin-doctor-conversations']);
    },
  });
}

// Hook to mark conversation as read
export function useMarkAdminDoctorConversationAsRead() {
  return useMutation({
    mutationFn: async (conversationId: string) => {
      await apiClient.post(`/admin-doctor-chat/conversations/${conversationId}/read`);
    },
  });
}

// Hook to assign conversation to admin
export function useAssignAdminDoctorConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async ({
      conversationId,
      adminUserId,
    }: {
      conversationId: string;
      adminUserId?: string;
    }) => {
      const response = await apiClient.put(
        `/admin-doctor-chat/conversations/${conversationId}/assign`,
        { adminUserId },
      );
      return response.data;
    },
    onSuccess: () => {
      void invalidatedQuery(['admin-doctor-conversations']);
    },
  });
}

// Hook to unassign conversation
export function useUnassignAdminDoctorConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      const response = await apiClient.put(
        `/admin-doctor-chat/conversations/${conversationId}/unassign`,
      );
      return response.data;
    },
    onSuccess: () => {
      void invalidatedQuery(['admin-doctor-conversations']);
    },
  });
}

// Hook to close conversation
export function useCloseAdminDoctorConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      const response = await apiClient.put(
        `/admin-doctor-chat/conversations/${conversationId}/close`,
      );
      return response.data;
    },
    onSuccess: () => {
      void invalidatedQuery(['admin-doctor-conversations']);
    },
  });
}
