import { redirect } from 'next/navigation';

import type { PatientGroup } from '@willow/db/client';
import { PatientGroupsList } from '@willow/db/client';

import { DoctorMessages } from '../_components/DoctorMessages';

('use-client');

const messagesGroup = ['all', 'unassigned', 'closed'];
type MessagesGroup = 'all' | 'unassigned' | 'closed';

export default function Page({
  params: { group },
}: {
  params: { group: MessagesGroup };
}) {
  if (!messagesGroup.includes(group)) return redirect('/messages/all');

  return <DoctorMessages group={group} />;
}
