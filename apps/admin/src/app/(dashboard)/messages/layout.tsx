import type { ReactNode } from 'react';

import { DoctorMessagesSidebar } from './_components/DoctorMessagesSidebar';

export default function MessagesPageLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <div className="font-neue grid grid-cols-[auto_1fr]">
      <DoctorMessagesSidebar />
      <section>
        <main className="relative h-screen overflow-y-scroll pb-0 pt-16">
          {children}
        </main>
      </section>
    </div>
  );
}
