'use client';

import { useRef, useState } from 'react';
import Image from 'next/image';
import { zodResolver } from '@hookform/resolvers/zod';
import imageCompression from 'browser-image-compression';
import { PaperclipIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import useMeasure from 'react-use-measure';
import { z } from 'zod';

import type { Conversation } from '@willow/chat';
import { Button } from '@willow/ui/base/button';
import { ChatInput } from '@willow/ui/base/chat-input';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import { Form, FormControl, FormField, FormItem } from '@willow/ui/base/form';
import { formatFilename } from '@willow/utils/format';

import { useSendAdminDoctorMessage, useUploadAdminDoctorFile } from '~/hooks/admin-doctor-chat';

const schema = z.object({
  message: z.string().min(1),
});

interface AdminDoctorMessageInputProps {
  conversation: Conversation;
}

export const AdminDoctorMessageInput = ({ conversation }: AdminDoctorMessageInputProps) => {
  const { mutateAsync: sendMessage, isPending: isSubmittingMessage } = 
    useSendAdminDoctorMessage();
  const { mutateAsync: uploadFile, isPending: isUploadingFile } = 
    useUploadAdminDoctorFile();

  const [chatRef, bounds] = useMeasure();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [isSendingImage, setIsSendingImage] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      message: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    if (!conversation) return;

    const { message } = data;

    try {
      await sendMessage({
        conversationId: conversation.id,
        content: message,
        contentType: 'text',
        role: 'Admin',
      });

      form.setValue('message', '');
      inputRef.current?.focus();
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
    }
  };

  const submitImage = async (image: File) => {
    if (!conversation) return;

    try {
      setIsSendingImage(true);
      const processedImage = await imageCompression(image, {
        maxWidthOrHeight: 1200,
        useWebWorker: true,
        fileType: 'image/jpeg',
        initialQuality: 0.8,
      });

      await uploadFile({
        conversationId: conversation.id,
        filename: image.name,
        file: processedImage,
      });

      await sendMessage({
        conversationId: conversation.id,
        content: image.name,
        contentType: 'image',
        role: 'Admin',
      });

      setSelectedImage(null);
      setIsSendingImage(false);
    } catch (error) {
      console.error('Error sending image:', error);
      setIsSendingImage(false);
    }
  };

  return (
    <>
      <div ref={chatRef} className="flex w-full flex-row items-end gap-2">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex w-full flex-row items-end gap-2"
          >
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <ChatInput
                      ref={inputRef}
                      placeholder="Type a message..."
                      {...field}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          form.handleSubmit(onSubmit)();
                        }
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              style={{ display: 'none' }}
              id="image-upload"
            />
            <label htmlFor="image-upload">
              <Button
                size="sm"
                variant="denimOutline"
                className="text-xs"
                type="button"
                asChild
              >
                <span>
                  <PaperclipIcon className="!h-4 !w-4" />
                </span>
              </Button>
            </label>
            <Button
              size="sm"
              variant="denim"
              className="text-xs text-white"
              type="submit"
              loading={isSubmittingMessage || isUploadingFile}
            >
              SEND MESSAGE
            </Button>
          </form>
        </Form>
      </div>

      {/* Image Preview Dialog */}
      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-md">
          {selectedImage && (
            <div className="flex flex-col gap-4">
              <div className="text-lg font-semibold">Send Image</div>
              <div className="relative h-64 w-full overflow-hidden rounded-lg">
                <Image
                  src={URL.createObjectURL(selectedImage)}
                  alt="Preview"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="text-sm text-gray-600">
                {formatFilename(selectedImage.name)}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="denimOutline"
                  onClick={() => setSelectedImage(null)}
                  disabled={isSendingImage}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="denim"
                  onClick={() => submitImage(selectedImage)}
                  loading={isSendingImage}
                  className="flex-1"
                >
                  Send Image
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};
