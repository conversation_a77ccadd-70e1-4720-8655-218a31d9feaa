'use client';

import { ChatProvider, Messages } from '@willow/chat';
import { Loader } from '@willow/ui/loader';

import { env } from '~/env';
import { useAdminDoctorConversation } from '~/hooks/admin-doctor-chat';

import { AdminDoctorMessageInput } from './AdminDoctorMessageInput';

interface AdminDoctorChatProps {
  conversationId: string;
  currentAdminUserId: string;
  accessToken: string;
}

export const AdminDoctorChat = ({ 
  conversationId, 
  currentAdminUserId, 
  accessToken 
}: AdminDoctorChatProps) => {
  const { data: conversation, isLoading } = useAdminDoctorConversation(conversationId);

  if (isLoading) {
    return (
      <div className="flex h-[calc(100%-130px)] items-center justify-center">
        <Loader size="md" />
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="flex h-[calc(100%-130px)] items-center justify-center">
        <div className="text-center text-gray-500">
          Conversation not found
        </div>
      </div>
    );
  }

  return (
    <ChatProvider
      apiUrl={env.NEXT_PUBLIC_API_URL}
      accessToken={accessToken}
      conversationId={conversationId}
      currentUserId={currentAdminUserId}
      autoMarkAsRead={true}
    >
      <div className="h-[calc(100%-130px)]">
        <div className="flex h-full flex-col gap-5 overflow-scroll pb-4 pr-2">
          {conversation.messages.length === 0 && <EmptyChat />}
          {conversation.messages.length > 0 && (
            <Messages
              messages={conversation.messages}
              participants={conversation.participants}
              currentUserId={currentAdminUserId}
              isLoading={isLoading}
              showPatientServicesNotification={false}
            />
          )}
        </div>
        <div className="w-full">
          <AdminDoctorMessageInput conversation={conversation} />
        </div>
      </div>
    </ChatProvider>
  );
};

const EmptyChat = () => {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-2.5 pb-20">
      <div className="flex flex-col items-center justify-center">
        <div className="text-xl font-semibold text-denim">
          Start a conversation
        </div>
        <div className="text-center text-xl font-medium text-denim text-opacity-60">
          Send a message to begin chatting with this doctor
        </div>
      </div>
    </div>
  );
};
