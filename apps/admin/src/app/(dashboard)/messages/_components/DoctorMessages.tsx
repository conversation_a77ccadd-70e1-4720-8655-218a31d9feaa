'use client';

import { useState } from 'react';
import { XIcon } from 'lucide-react';

import { cn } from '@willow/ui';
import { Avatar, AvatarFallback } from '@willow/ui/base/avatar';
import { Button } from '@willow/ui/base/button';

import type { MessagesGroup } from './DoctorMessagesSidebar';
import { DoctorAdminConversation } from '../../patients/_components/DoctorAdminConversation';

const conversations = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];

export const DoctorMessages = ({ group }: { group: MessagesGroup }) => {
  const [selectedConversation, setSelectedConversation] = useState(0);
  return (
    <div className="flex h-full flex-row overflow-scroll">
      <div className="w-1/4">
        <div className="mb-4 px-2 text-2xl font-medium text-slate-600">
          {typeof group === 'string'
            ? group.charAt(0).toUpperCase() + group.slice(1)
            : group}
        </div>
        <div className="h-[calc(100%-52px)] overflow-scroll">
          {conversations.map((e) => (
            <Conversation
              key={e}
              id={e}
              isSelected={e === selectedConversation}
              setSelectedConversation={setSelectedConversation}
            />
          ))}
        </div>
      </div>
      <div className="h-full w-3/4 border-l-[0.5px] border-slate-200">
        <AssignPatientBanner />
        <div className="h-full px-4">
          <DoctorAdminConversation />
        </div>
      </div>
    </div>
  );
};

const Conversation = ({
  isSelected,
  setSelectedConversation,
  id,
}: {
  id: number;
  isSelected: boolean;
  setSelectedConversation: (id: number) => void;
}) => {
  return (
    <div
      onClick={() => setSelectedConversation(id)}
      className={cn(
        'flex cursor-pointer flex-col gap-4 border-[0.5px] border-slate-200 bg-white px-3 py-2',
        {
          'bg-stone-light': isSelected,
        },
      )}
    >
      <div className="flex flex-row items-center gap-4">
        <Avatar className="h-10 w-10">
          {/* <AvatarImage src={genAssetLink(row.original.image)} /> */}
          <AvatarFallback className="font-bold uppercase text-denim-light">
            {/* {row.original.user?.firstName?.[0]} */}JB
            {/* {row.original.user?.lastName?.[0]} */}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-2">
          <div className="text-xs font-medium text-slate-600">Patient Name</div>
          <div className="text-xs font-normal text-zinc-900">Dr. Name</div>
        </div>
      </div>
      <div className="flex flex-row items-end gap-4">
        <div className="flex-1 text-xs font-normal text-zinc-900">
          Message preview box This is two lines long
        </div>
        <div className="text-xs font-normal text-zinc-500">June 05, 6PM</div>
      </div>
    </div>
  );
};

const AssignPatientBanner = () => {
  return (
    <div className="absolute top-0 z-50 flex w-3/4 flex-row items-center justify-between bg-stone-light py-3 pl-4 pr-8">
      <div className="flex flex-row items-center gap-4">
        <Avatar className="h-10 w-10">
          {/* <AvatarImage src={genAssetLink(row.original.image)} /> */}
          <AvatarFallback className="font-bold uppercase text-denim-light">
            {/* {row.original.user?.firstName?.[0]} */}JB
            {/* {row.original.user?.lastName?.[0]} */}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-2">
          <div className="text-xs font-medium text-slate-600">Patient Name</div>
          <div className="text-xs font-normal text-zinc-900">Dr. Name</div>
        </div>
      </div>

      <div className="flex flex-row items-center gap-2">
        <Button
          size="sm"
          variant="denim"
          className="h-8 text-xs font-semibold text-white"
        >
          UNASSIGN
        </Button>
        <Button
          size="sm"
          variant="denimOutline"
          className="h-8 text-xs font-semibold"
        >
          CLOSE
          <XIcon className="!h-4 !w-4" />
        </Button>
      </div>
    </div>
  );
};
