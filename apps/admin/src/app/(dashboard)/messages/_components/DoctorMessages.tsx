'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { XIcon } from 'lucide-react';

import { cn } from '@willow/ui';
import { Avatar, AvatarFallback } from '@willow/ui/base/avatar';
import { Button } from '@willow/ui/base/button';
import { Loader } from '@willow/ui/loader';

import type { MessagesGroup } from './DoctorMessagesSidebar';
import type { AdminConversationFilter } from '~/hooks/chat';
import { AdminChatProvider } from '~/components/chat/AdminChatProvider';
import { env } from '~/env';
import {
  useAdminPatientConversations,
  useCloseAdminPatientConversation,
  useUnassignAdminPatientConversation,
} from '~/hooks/chat';
import { useProfile } from '~/hooks/useProfile';
import { AdminPatientConversation } from '../../patients/_components/DoctorAdminConversation';

export const DoctorMessages = ({ group }: { group: MessagesGroup }) => {
  const [selectedConversationId, setSelectedConversationId] = useState<
    string | null
  >(null);
  const profile = useProfile();

  // Map MessagesGroup to AdminConversationFilter
  const filterMap: Record<MessagesGroup, AdminConversationFilter> = {
    all: 'all',
    unassigned: 'unassigned',
    closed: 'closed',
  };

  const { data: conversationsData, isLoading } = useAdminPatientConversations(
    filterMap[group],
    1,
    50,
  );

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader size="lg" />
      </div>
    );
  }

  const conversations = conversationsData?.conversation || [];
  const selectedConversation = conversations.find(
    (c) => c.id === selectedConversationId,
  );

  return (
    <div className="flex h-full flex-row overflow-scroll">
      <div className="w-1/4">
        <div className="mb-4 px-2 text-2xl font-medium text-slate-600">
          {typeof group === 'string'
            ? group.charAt(0).toUpperCase() + group.slice(1)
            : group}
        </div>
        <div className="h-[calc(100%-52px)] overflow-scroll">
          {conversations.length === 0 && (
            <div className="py-8 text-center text-gray-500">
              No conversations found
            </div>
          )}
          {conversations.map((conversation) => (
            <ConversationItem
              key={conversation.id}
              conversation={conversation}
              isSelected={selectedConversationId === conversation.id}
              onSelect={setSelectedConversationId}
            />
          ))}
        </div>
      </div>
      <div className="h-full w-3/4 border-l-[0.5px] border-slate-200">
        <AssignPatientBanner selectedConversation={selectedConversation} />
        <div className="h-full px-4">
          {selectedConversation && profile?.accessToken ? (
            <AdminChatProvider
              apiUrl={env.NEXT_PUBLIC_API_URL}
              accessToken={profile.accessToken}
              conversationId={selectedConversation.id}
              adminUserId={profile.id}
            >
              <AdminPatientConversation
                conversation={selectedConversation as any}
                currentAdminUserId={profile.id}
              />
            </AdminChatProvider>
          ) : (
            <div className="flex h-full items-center justify-center">
              <div className="text-center text-gray-500">
                Select a conversation to start chatting
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const ConversationItem = ({
  conversation,
  isSelected,
  onSelect,
}: {
  conversation: any;
  isSelected: boolean;
  onSelect: (id: string) => void;
}) => {
  const patientName = `${conversation.patient.user.firstName} ${conversation.patient.user.lastName}`;
  const patientInitials = `${conversation.patient.user.firstName[0]}${conversation.patient.user.lastName[0]}`;
  const lastMessageTime = conversation.lastMessage
    ? format(new Date(conversation.lastMessage.createdAt), 'MMM dd, h:mm a')
    : '';

  return (
    <div
      onClick={() => onSelect(conversation.id)}
      className={cn(
        'flex cursor-pointer flex-col gap-4 border-[0.5px] border-slate-200 bg-white px-3 py-2',
        {
          'bg-stone-light': isSelected,
        },
      )}
    >
      <div className="flex flex-row items-center gap-4">
        <Avatar className="h-10 w-10">
          <AvatarFallback className="font-bold uppercase text-denim-light">
            {patientInitials}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-2">
          <div className="text-xs font-medium text-slate-600">
            {patientName}
          </div>
          <div className="text-xs font-normal text-zinc-900">
            {conversation.assignedAdmin
              ? `${conversation.assignedAdmin.firstName} ${conversation.assignedAdmin.lastName}`
              : 'Unassigned'}
          </div>
        </div>
      </div>
      <div className="flex flex-row items-end gap-4">
        <div className="flex-1 text-xs font-normal text-zinc-900">
          {conversation.lastMessageText || 'No messages yet'}
        </div>
        <div className="text-xs font-normal text-zinc-500">
          {lastMessageTime}
        </div>
      </div>
      {conversation.unreadMessages > 0 && (
        <div className="absolute right-2 top-2 rounded-full bg-red-500 px-2 py-1 text-xs text-white">
          {conversation.unreadMessages}
        </div>
      )}
    </div>
  );
};

const AssignPatientBanner = ({
  selectedConversation,
}: {
  selectedConversation?: any;
}) => {
  const { mutate: unassignConversation } =
    useUnassignAdminPatientConversation();
  const { mutate: closeConversation } = useCloseAdminPatientConversation();

  if (!selectedConversation) {
    return null;
  }

  const patientName = `${selectedConversation.patient.user.firstName} ${selectedConversation.patient.user.lastName}`;
  const patientInitials = `${selectedConversation.patient.user.firstName[0]}${selectedConversation.patient.user.lastName[0]}`;

  const handleUnassign = () => {
    unassignConversation(selectedConversation.id);
  };

  const handleClose = () => {
    closeConversation(selectedConversation.id);
  };

  return (
    <div className="absolute top-0 z-50 flex w-3/4 flex-row items-center justify-between bg-stone-light py-3 pl-4 pr-8">
      <div className="flex flex-row items-center gap-4">
        <Avatar className="h-10 w-10">
          <AvatarFallback className="font-bold uppercase text-denim-light">
            {patientInitials}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-2">
          <div className="text-xs font-medium text-slate-600">
            {patientName}
          </div>
          <div className="text-xs font-normal text-zinc-900">
            Status: {selectedConversation.status}
          </div>
        </div>
      </div>

      <div className="flex flex-row items-center gap-2">
        {selectedConversation.assignedAdminId && (
          <Button
            size="sm"
            variant="denim"
            className="h-8 text-xs font-semibold text-white"
            onClick={handleUnassign}
          >
            UNASSIGN
          </Button>
        )}
        {selectedConversation.status !== 'closed' && (
          <Button
            size="sm"
            variant="denimOutline"
            className="h-8 text-xs font-semibold"
            onClick={handleClose}
          >
            CLOSE
            <XIcon className="!h-4 !w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};
