import type { PatientGroup } from '@willow/db/client';

import PanelLeftIcon from '~/assets/svg/panel-left.svg';
import { NavbarItem, NavbarSection } from '~/components/NavbarItem';
import { useStreamingPatientsGroupCounts } from '~/hooks/patients';

export const DoctorMessagesSidebar = () => {
  return (
    <aside className="scrollbar-hidden h-screen w-[284px] overflow-y-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6 text-[13px]">
        <div className="flex justify-between text-denim">
          <h1 className="text-2xl">Messages</h1>
          <div className="flex items-center gap-2">
            <PanelLeftIcon />
          </div>
        </div>

        <NavbarSection title="My Inbox">
          <DoctorMessagesSidebarLink title="All" group="all" count={0} />
          <DoctorMessagesSidebarLink
            title="Unassigned"
            group="unassigned"
            count={0}
          />
          <DoctorMessagesSidebarLink title="Closed" group="closed" count={0} />
        </NavbarSection>
      </div>
    </aside>
  );
};

export type MessagesGroup = 'all' | 'unassigned' | 'closed';

const DoctorMessagesSidebarLink = ({
  title,
  group,
  count = 0,
  processedGroups,
  isComplete,
}: {
  title: string;
  group: MessagesGroup;
  count?: number;
  processedGroups?: Set<string>;
  isComplete?: boolean;
}) => {
  return (
    <NavbarItem
      href={`/messages/${group}`}
      title={title}
      count={count}
      countClassName={'text-gray-400'}
    />
  );
};
