'use client';

import { useEffect, useState } from 'react';

import { Loader } from '@willow/ui/loader';

import { AdminChatProvider } from '~/components/chat/AdminChatProvider';
import { env } from '~/env';
import { useCreateAdminPatientConversation } from '~/hooks/chat';
import { useProfile } from '~/hooks/useProfile';

import { AdminPatientConversation } from './DoctorAdminConversation';

interface AdminPatientChatWrapperProps {
  patientId: string;
}

export const AdminPatientChatWrapper = ({ patientId }: AdminPatientChatWrapperProps) => {
  const [conversationId, setConversationId] = useState<string | null>(null);
  const profile = useProfile();
  const { mutateAsync: createConversation, isPending: isCreating } = useCreateAdminPatientConversation();

  useEffect(() => {
    const initializeConversation = async () => {
      if (!patientId || !profile?.id) return;

      try {
        const result = await createConversation({ patientId });
        setConversationId(result.conversationId);
      } catch (error) {
        console.error('Error creating/getting conversation:', error);
      }
    };

    void initializeConversation();
  }, [patientId, profile?.id, createConversation]);

  if (isCreating || !conversationId || !profile?.accessToken) {
    return (
      <div className="flex h-[calc(100%-130px)] items-center justify-center">
        <Loader size="md" />
      </div>
    );
  }

  // Create a mock conversation object for the component
  const mockConversation = {
    id: conversationId,
    messages: [],
    participants: {},
    // Add other required properties as needed
  };

  return (
    <AdminChatProvider
      apiUrl={env.NEXT_PUBLIC_API_URL}
      accessToken={profile.accessToken}
      conversationId={conversationId}
      adminUserId={profile.id}
    >
      <AdminPatientConversation 
        conversation={mockConversation as any}
        currentAdminUserId={profile.id}
      />
    </AdminChatProvider>
  );
};
