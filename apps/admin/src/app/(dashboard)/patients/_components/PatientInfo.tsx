import { useMemo, useState } from 'react';
import Link from 'next/link';
import { format } from 'date-fns';
import {
  MessagesSquareIcon,
  PencilIcon,
  StickyNoteIcon,
  XIcon,
} from 'lucide-react';
import { useQueryState } from 'nuqs';

import type { TreatmentItemAPI } from '@willow/db/client';
import { Avatar, AvatarFallback, AvatarImage } from '@willow/ui/base/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@willow/ui/base/tabs';
import { useToast } from '@willow/ui/base/use-toast';
import { DateDistance } from '@willow/ui/date-distance';
import { Loader } from '@willow/ui/loader';
import { formatBirthDate } from '@willow/utils/format';

import { env } from '~/env';
import { usePatientAssetLink } from '~/hooks/links';
import { useGetPatientActivityLog, useGetPatientInfo } from '~/hooks/patients';
import { useGetPatientTreatments } from '~/hooks/treatments';
import { AdminPatientChatWrapper } from './AdminPatientChatWrapper';
import { PatientActivityLog } from './patient-activity-log';
import { PatientCancelledBanner } from './PatientCancelledBanner';
import { PatientInfoField } from './PatientInfoField';
import { PatientIntakeHistory } from './PatientIntakeHistory';
import { PatientMiscMenu } from './PatientMiscMenu/patient-misc-menu';
import { PresctiptionCard } from './PrescriptionCard';
import { PrescriptionHistoryDialog } from './PrescriptionHistoryDialog';
import { TreatmentsLogsDialog } from './TreatmentsLogsDialog';

export const PatientInfo = ({
  patientId,
  handleClose,
}: {
  patientId: string;
  handleClose: () => void;
}) => {
  const [selectedTreatment, setSelectedTreatment] = useState<{
    patientId: string;
    treatment: TreatmentItemAPI;
  } | null>(null);
  const [isDisplayIntakeHistory, setIsDisplayIntakeHistory] = useState(false);
  const [openPrescriptionHistoryDialog, setPrescriptionHistoryDialog] =
    useState(false);

  const [selectedTreatmentId, setSelectedTreatmentId] = useState('');

  const { data: notes, isLoading: isNotesLoading } = useGetPatientActivityLog(
    patientId,
    {
      includes: 'PATIENT_NOTE_CREATED',
    },
  );
  const { data: patient, error, isPending } = useGetPatientInfo(patientId);
  const { data: patientTreatments } = useGetPatientTreatments(patientId);
  const { genAssetLink } = usePatientAssetLink();

  const [section, setSection] = useQueryState('section', {
    defaultValue: 'prescriptions',
    parse: (value) => {
      return ['prescriptions', 'notes', 'activity'].includes(value)
        ? value
        : 'prescriptions';
    },
  });
  const { toast } = useToast();
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: 'Copied',
        variant: 'default',
        duration: 1000,
      });
    } catch {
      toast({
        title: 'An error occurred while copying to clipboard',
        variant: 'destructive',
        duration: 1000,
      });
    }
  };

  const nextFollowUp = useMemo(
    () =>
      patient?.PatientFollowUp &&
      patient.PatientFollowUp?.length > 0 &&
      patient.PatientFollowUp?.[0]?.scheduledAt
        ? format(
            new Date(patient?.PatientFollowUp[0]?.scheduledAt),
            'MMMM d, yyyy',
          )
        : 'No follow-up scheduled',
    [patient?.PatientFollowUp],
  );

  if (isPending) return <Loader className="w-full bg-white" />;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="relative flex h-full w-full grow bg-white">
      <PatientCancelledBanner patient={patient} />
      <div className="flex h-full w-1/3 flex-col justify-between bg-stone-light px-6 py-10">
        <div className="flex flex-col gap-6">
          <div className="flex justify-between">
            <div className="flex flex-row items-center gap-4">
              <div className="relative h-10 w-10">
                <Avatar>
                  <AvatarImage src={genAssetLink(patient.facePhoto)} />
                  <AvatarFallback className="font-bold uppercase text-denim-light">
                    {patient.user.firstName?.[0]}
                    {patient.user.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div>
                <div
                  className="text-base font-medium text-dark"
                  onClick={() =>
                    copyToClipboard(
                      `${patient.user.firstName} ${patient.user.lastName}`,
                    )
                  }
                >
                  {patient.user.firstName} {patient.user.lastName}
                </div>
                <div
                  className="text-[11px] font-medium text-stone/70"
                  onClick={() =>
                    copyToClipboard(
                      patient.birthDate
                        ? formatBirthDate(new Date(patient.birthDate))
                        : '-',
                    )
                  }
                >
                  {patient.birthDate
                    ? formatBirthDate(new Date(patient.birthDate))
                    : '-'}
                </div>
              </div>
            </div>
            <div className="self-start">
              <PatientMiscMenu
                patient={patient}
                closeInfoDialog={handleClose}
                setIsDisplayIntakeHistory={setIsDisplayIntakeHistory}
              />
            </div>
          </div>
          <div className="flex flex-col gap-4">
            <div className="text-sm font-normal text-orange">
              Contact Info & Address
            </div>
            <PatientInfoField
              label="Location"
              value={patient.state.code}
              disabled={!patient.state.code}
              copiable
            />
            <PatientInfoField
              label="Email Address"
              value={patient.user.email}
              disabled={!patient.user.email}
              copiable
            />
            <PatientInfoField
              label="Phone"
              value={patient.user.phone ?? ''}
              disabled={!patient.user.phone}
              copiable
            />
            <PatientInfoField
              label="Card Last 4 Digits"
              value={patient.paymentMethod?.data?.card?.last4 ?? ''}
              disabled={!patient.paymentMethod?.data?.card?.last4}
            />
            <PatientInfoField label="Timezone" value={'US/Central'} />
            <PatientInfoField
              label="Shipping Address"
              value={[
                patient.shippingAddress?.address1,
                patient.shippingAddress?.address2,
                patient.shippingAddress?.city,
                patient.shippingAddress?.zip,
                patient.shippingAddress?.state.code,
              ]
                .filter(Boolean)
                .join(', ')}
              disabled={
                [
                  patient.shippingAddress?.address1,
                  patient.shippingAddress?.address2,
                  patient.shippingAddress?.city,
                  patient.shippingAddress?.zip,
                  patient.shippingAddress?.state.code,
                ].filter(Boolean).length === 0
              }
              copiable
            />
            <PatientInfoField
              label="Billing Address"
              value={[
                patient.billingAddress?.address1,
                patient.billingAddress?.address2,
                patient.billingAddress?.city,
                patient.billingAddress?.zip,
                patient.billingAddress?.state.code,
              ]
                .filter(Boolean)
                .join(', ')}
              disabled={
                [
                  patient.billingAddress?.address1,
                  patient.billingAddress?.address2,
                  patient.billingAddress?.city,
                  patient.billingAddress?.zip,
                  patient.billingAddress?.state.code,
                ].filter(Boolean).length === 0
              }
              copiable
            />
          </div>

          <div className="flex flex-col gap-4">
            <div className="text-sm font-normal text-orange">Medical Info</div>
            <PatientInfoField
              label="Pharmacy"
              value={patient.pharmacy.name ? patient.pharmacy.name : '_'}
              disabled={!patient.pharmacy.name}
              copiable
            />
            <PatientInfoField
              label="Next Follow-Up"
              value={nextFollowUp}
              disabled={
                patient.PatientFollowUp.length === 0 ||
                !patient.PatientFollowUp[0]?.scheduledAt
              }
            />
            <PatientInfoField
              label="Doctor"
              value={
                patient.doctor
                  ? `Dr. ${patient.doctor.user.firstName} ${patient.doctor.user.lastName}`
                  : '_'
              }
              disabled={
                !patient.doctor?.user.firstName || !patient.doctor.user.lastName
              }
              copiable
            />
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">
                Last Message with Doctor Sent
              </div>
              <div>
                {patient.conversation?.updatedAt ? (
                  <DateDistance
                    className="text-xs font-normal text-dark"
                    date={new Date(patient.conversation?.updatedAt)}
                    cutoffUnit="hour"
                    cutoffValue={2}
                    onClick={(date) => copyToClipboard(`${date}`)}
                  />
                ) : (
                  <div className="text-xs font-normal text-dark">_</div>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <div className="text-xs font-medium text-stone/70">Accounts</div>
          <Link
            href={`https://app.intercom.com/apps/${env.NEXT_PUBLIC_INTERCOM_APP_ID}/users/show?user_id=${patient.userId}`}
            className="text-xs font-normal text-denim"
            target="_blank"
          >
            Intercom
          </Link>
          <Link
            href={`https://dashboard.stripe.com/customers/${patient.stripeCustomerId}`}
            className="text-xs font-normal text-denim"
            target="_blank"
          >
            Stripe
          </Link>
          <Link
            href={`https://mixpanel.com/project/3171468/view/3683400/app/profile#distinct_id=${patient.userId}`}
            className="text-xs font-normal text-denim"
            target="_blank"
          >
            Mixpanel
          </Link>
        </div>
      </div>
      <div className="flex w-2/3 flex-col">
        {isDisplayIntakeHistory ? (
          <div className="p-10">
            <div className="flex flex-row justify-end text-denim">
              <XIcon
                size={24}
                className="cursor-pointer"
                onClick={() => setIsDisplayIntakeHistory(false)}
              />
            </div>
            <PatientIntakeHistory patientId={patientId} />
          </div>
        ) : (
          <Tabs
            defaultValue={section}
            onValueChange={setSection}
            className="relative grid h-full w-full grid-rows-[auto_1fr]"
          >
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="flex flex-row justify-end text-denim">
                <XIcon
                  size={24}
                  className="cursor-pointer"
                  onClick={handleClose}
                />
              </div>
              <TabsList className="h-fit w-full justify-start gap-5 rounded-none border-b-[1px] border-denim-muted bg-white p-0 text-denim">
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="prescriptions"
                >
                  <StickyNoteIcon size={16} className="mr-1" />
                  <div>Prescriptions</div>
                </TabsTrigger>
                <TabsTrigger
                  value="notes"
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                >
                  <PencilIcon size={16} className="mr-1" />
                  <div>
                    Notes
                    {`(${isNotesLoading ? 'loading...' : (notes?.length ?? 0)})`}
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="doctorMessaging"
                >
                  <MessagesSquareIcon size={16} className="mr-1" />
                  <div>Doctor Messaging</div>
                </TabsTrigger>
              </TabsList>
            </div>
            <TabsContent className="relative h-full" value="notes">
              <PatientActivityLog patientId={patient.id} />
            </TabsContent>
            <TabsContent
              className="overflow-scroll px-10"
              value="prescriptions"
            >
              <div className="mb-5 mt-10 text-2xl font-medium text-denim">
                Active Prescriptions
              </div>
              <div className="flex flex-col gap-4 pb-10">
                {selectedTreatment && (
                  <TreatmentsLogsDialog
                    patientId={selectedTreatment.patientId}
                    treatment={selectedTreatment.treatment}
                    onClose={() => setSelectedTreatment(null)}
                  />
                )}
                {patientTreatments?.map((treatment) => (
                  <PresctiptionCard
                    key={treatment.treatmentId}
                    patientId={patientId}
                    treatment={treatment}
                    onClick={({ patientId, treatment }) =>
                      setSelectedTreatment({ patientId, treatment })
                    }
                    handlePrescriptionHistoryClick={() => {
                      setPrescriptionHistoryDialog(true);
                      setSelectedTreatmentId(treatment.treatmentId);
                    }}
                    isSelected={false}
                  />
                ))}
              </div>
            </TabsContent>
            <TabsContent
              className="overflow-scroll px-10"
              value="doctorMessaging"
            >
              <AdminPatientChatWrapper patientId={patientId} />
            </TabsContent>
          </Tabs>
        )}
      </div>

      {patientTreatments && (
        <PrescriptionHistoryDialog
          treatments={patientTreatments}
          setSelectedTreatmentId={setSelectedTreatmentId}
          patientId={patientId}
          selectedTreatmentId={selectedTreatmentId}
          open={openPrescriptionHistoryDialog}
          onClose={setPrescriptionHistoryDialog}
        />
      )}
    </div>
  );
};
