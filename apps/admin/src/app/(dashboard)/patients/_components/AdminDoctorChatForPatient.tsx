'use client';

import { useEffect, useState } from 'react';

import { Loader } from '@willow/ui/loader';

import { env } from '~/env';
import {
  useAdminDoctorConversations,
  useCreateAdminDoctorConversation,
} from '~/hooks/admin-doctor-chat';
import { useGetPatientInfo } from '~/hooks/patients';
import { useAccessToken } from '~/hooks/useHasAccessToken';
import { useProfile } from '~/hooks/useProfile';
import { AdminDoctorChat } from '../../messages/_components/AdminDoctorChat';

interface AdminDoctorChatForPatientProps {
  patientId: string;
}

export const AdminDoctorChatForPatient = ({
  patientId,
}: AdminDoctorChatForPatientProps) => {
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const profile = useProfile();
  const accessToken = useAccessToken();
  const { mutateAsync: createConversation } =
    useCreateAdminDoctorConversation();

  // Get patient info to access doctor information
  const { data: patient, isLoading: isLoadingPatient } =
    useGetPatientInfo(patientId);

  // Get all admin-doctor conversations to find one for this patient
  const { data: conversationsData, isLoading: isLoadingConversations } =
    useAdminDoctorConversations(
      'all',
      1,
      100, // Get more to ensure we find the conversation for this patient
    );

  useEffect(() => {
    const initializeConversation = async () => {
      if (
        !patientId ||
        !profile?.id ||
        isLoadingConversations ||
        isLoadingPatient
      )
        return;

      try {
        setIsInitializing(true);

        // Look for existing conversation for this patient
        const existingConversation = conversationsData?.conversation?.find(
          (conv) => conv.patientId === patientId,
        );

        if (existingConversation) {
          setConversationId(existingConversation.id);
        } else if (patient?.doctor?.id) {
          // Create new conversation if patient has a doctor assigned
          const result = await createConversation({
            patientId,
            doctorUserId: patient.doctor.id,
          });
          setConversationId(result.conversationId);
        } else {
          // Patient doesn't have a doctor assigned
          console.log('Patient has no doctor assigned:', patientId);
        }
      } catch (error) {
        console.error('Error initializing conversation:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    void initializeConversation();
  }, [
    patientId,
    profile?.id,
    conversationsData,
    isLoadingConversations,
    isLoadingPatient,
    patient,
    createConversation,
  ]);

  if (isLoadingConversations || isLoadingPatient || isInitializing) {
    return (
      <div className="flex h-[calc(100%-130px)] items-center justify-center">
        <Loader size="md" />
      </div>
    );
  }

  if (accessToken) {
    return (
      <div className="flex h-[calc(100%-130px)] items-center justify-center">
        <div className="text-center text-gray-500">
          <div className="mb-2 text-lg font-semibold">
            Authentication Required
          </div>
          <div>Please log in to access the chat.</div>
        </div>
      </div>
    );
  }

  if (!conversationId) {
    return (
      <div className="flex h-[calc(100%-130px)] items-center justify-center">
        <div className="text-center text-gray-500">
          <div className="mb-2 text-lg font-semibold">
            No Conversation Available
          </div>
          {!patient?.doctor ? (
            <div>
              <div>This patient doesn't have a doctor assigned.</div>
              <div className="mt-2 text-sm">
                Assign a doctor to this patient to enable admin-doctor
                conversations.
              </div>
            </div>
          ) : (
            <div>
              <div>No admin-doctor conversation exists for this patient.</div>
              <div className="mt-2 text-sm">
                A conversation will be created automatically when you send the
                first message.
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="h-[calc(100%-60px)]">
      {' '}
      {/* Adjust height to fit within tab */}
      <AdminDoctorChat
        conversationId={conversationId}
        currentAdminUserId={profile.id}
        accessToken={accessToken ?? ''}
      />
    </div>
  );
};
