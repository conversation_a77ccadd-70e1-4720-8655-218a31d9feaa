import { cn } from '@willow/ui';
import { Avatar, AvatarFallback } from '@willow/ui/base/avatar';

import { MessageInput } from './MessageInput';

export const DoctorAdminConversation = () => {
  return (
    <div className="h-[calc(100%-130px)]">
      <div className="flex h-full flex-col gap-5 overflow-scroll pb-4 pr-2">
        <div className="w-f mb-9 mt-4 h-3 justify-start text-center text-xs font-normal text-zinc-500">
          June 05, 2025
        </div>
        <Message
          message="Hendrerit in facilisi enim rhoncus tempus at egestas viverra
              justo. Porttitor netus amet, tellus in. Est dolor risus dignissim
              quisque."
          date="6:00 PM"
          userId="userId01"
          profileId="userId02"
          avatar="JB"
        />

        <Message
          message="Hendrerit in facilisi enim rhoncus tempus at egestas viverra
              justo. Porttitor netus amet, tellus in. Est dolor risus dignissim
              quisque."
          date="6:00 PM"
          userId="userId01"
          profileId="userId01"
          avatar="JB"
        />
      </div>
      <div className="w-full">
        <MessageInput />
      </div>
    </div>
  );
};

const Message = ({
  message,
  date,
  avatar,
  userId,
  profileId,
}: {
  message: string;
  date: string;
  avatar: string;
  userId: string;
  profileId: string;
}) => {
  const isConnectedUser = profileId === userId;
  return (
    <div
      className={cn('flex flex-row gap-2', {
        'flex-row-reverse': isConnectedUser,
      })}
    >
      <Avatar className="h-10 w-10">
        {/* <AvatarImage src={genAssetLink(row.original.image)} /> */}
        <AvatarFallback className="font-bold uppercase text-denim-light">
          {/* {row.original.user?.firstName?.[0]} */}
          {avatar}
          {/* {row.original.user?.lastName?.[0]} */}
        </AvatarFallback>
      </Avatar>
      <div
        className={cn('bg-stone-light px-[10px] py-2', {
          'bg-[#48638B]': isConnectedUser,
        })}
      >
        <div
          className={cn('mb-3 text-xs font-normal text-zinc-900', {
            'text-white': isConnectedUser,
          })}
        >
          {message}
        </div>
        <div
          className={cn('w-f text-right text-[7px] font-normal text-zinc-500', {
            'text-white': isConnectedUser,
          })}
        >
          {date}
        </div>
      </div>
    </div>
  );
};
