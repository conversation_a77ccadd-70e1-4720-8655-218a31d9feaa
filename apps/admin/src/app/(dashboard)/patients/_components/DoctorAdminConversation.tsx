'use client';

import { format } from 'date-fns';

import type { Conversation } from '@willow/chat';
import { Messages } from '@willow/chat';
import { Loader } from '@willow/ui/loader';

import { useAdminChat } from '~/components/chat/AdminChatProvider';
import { MessageInput } from './MessageInput';

interface AdminPatientConversationProps {
  conversation: Conversation;
  currentAdminUserId: string;
}

export const AdminPatientConversation = ({
  conversation,
  currentAdminUserId,
}: AdminPatientConversationProps) => {
  const { messages, isLoading } = useAdminChat();

  if (isLoading) {
    return (
      <div className="flex h-[calc(100%-130px)] items-center justify-center">
        <Loader size="md" />
      </div>
    );
  }

  return (
    <div className="h-[calc(100%-130px)]">
      <div className="flex h-full flex-col gap-5 overflow-scroll pb-4 pr-2">
        {messages.length === 0 && <EmptyChat />}
        {messages.length > 0 && (
          <Messages
            messages={messages}
            participants={conversation.participants}
            currentUserId={currentAdminUserId}
            isLoading={isLoading}
            showPatientServicesNotification={false}
          />
        )}
      </div>
      <div className="w-full">
        <MessageInput conversation={conversation} />
      </div>
    </div>
  );
};

const EmptyChat = () => {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-2.5 pb-20">
      <div className="flex flex-col items-center justify-center">
        <div className="text-xl font-semibold text-denim">
          Start a conversation
        </div>
        <div className="text-center text-xl font-medium text-denim text-opacity-60">
          Send a message to begin chatting with this patient
        </div>
      </div>
    </div>
  );
};
