'use client';

import {
  createContext,
  useContext,
  useEffect,
  useReducer,
  useRef,
} from 'react';
import { io } from 'socket.io-client';

import type { Conversation, Message } from '@willow/chat';
import { useInvalidatedQuery } from '@willow/utils/react-query';

import { useAdminPatientConversation, useMarkAdminPatientConversationAsRead } from '~/hooks/chat';

interface AdminChatState {
  messages: Message[];
  isLoading: boolean;
  error?: Error;
}

type AdminChatAction =
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: Error };

interface AdminChatContextType {
  messages: Message[];
  isLoading: boolean;
  error?: Error;
  conversation?: Conversation;
}

interface AdminChatProviderProps {
  children: React.ReactNode;
  apiUrl: string;
  accessToken: string;
  conversationId: string;
  adminUserId: string;
  autoMarkAsRead?: boolean;
}

const AdminChatContext = createContext<AdminChatContextType | undefined>(undefined);

function adminChatReducer(state: AdminChatState, action: AdminChatAction): AdminChatState {
  switch (action.type) {
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    case 'ADD_MESSAGE':
      if (state.messages.find((msg) => msg.id === action.payload.id)) {
        return state;
      }
      return { ...state, messages: [...state.messages, action.payload] };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
}

export function AdminChatProvider({
  children,
  apiUrl,
  accessToken,
  conversationId,
  adminUserId,
  autoMarkAsRead = true,
}: AdminChatProviderProps) {
  const invalidatedQuery = useInvalidatedQuery();
  const [state, dispatch] = useReducer(adminChatReducer, {
    messages: [],
    isLoading: false,
  });

  const chatRef = useRef(conversationId);
  const { data: conversation, isLoading } = useAdminPatientConversation(conversationId);
  const { mutate: markAsRead } = useMarkAdminPatientConversationAsRead();

  useEffect(() => {
    return () => {
      void invalidatedQuery(['admin-patient-conversation', conversationId]);
    };
  }, [conversationId, invalidatedQuery]);

  // Initialize messages from conversation
  useEffect(() => {
    if (conversation?.messages) {
      dispatch({ type: 'SET_MESSAGES', payload: conversation.messages });
    }
  }, [conversation]);

  // Update loading state
  useEffect(() => {
    dispatch({ type: 'SET_LOADING', payload: isLoading });
  }, [isLoading]);

  // Socket connection for real-time updates
  useEffect(() => {
    if (!accessToken) return;

    const socket = io(`${apiUrl}/chat`, {
      autoConnect: false,
      auth: { token: accessToken },
      transports: ['websocket'],
    });

    socket.on('connect', () => {
      console.log('Connected to admin chat socket');
    });

    socket.on('connect_error', (err) => {
      console.error('Admin chat socket connection error:', err);
      dispatch({ type: 'SET_ERROR', payload: err });
    });

    socket.on('message', (data: Message) => {
      if (conversationId && data.conversationId !== chatRef.current) return;
      dispatch({ type: 'ADD_MESSAGE', payload: data });

      // Auto mark as read when receiving messages
      if (autoMarkAsRead) {
        markAsRead(conversationId);
      }

      // Don't invalidate watcher for admin's own messages
      if (adminUserId === data.userId) return;
      void invalidatedQuery(['admin-patient-conversations']);
    });

    socket.connect();

    return () => {
      socket.disconnect();
    };
  }, [apiUrl, accessToken, conversationId, autoMarkAsRead, markAsRead, adminUserId, invalidatedQuery]);

  const contextValue: AdminChatContextType = {
    messages: state.messages,
    isLoading: state.isLoading,
    error: state.error,
    conversation,
  };

  return (
    <AdminChatContext.Provider value={contextValue}>
      {children}
    </AdminChatContext.Provider>
  );
}

export function useAdminChat() {
  const context = useContext(AdminChatContext);
  if (!context) {
    throw new Error('useAdminChat must be used within AdminChatProvider');
  }
  return context;
}
