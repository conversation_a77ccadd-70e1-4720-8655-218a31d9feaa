'use client';

import { useRef, useState } from 'react';
import Image from 'next/image';
import { Dialog } from '@/components/ui/dialog';
import { env } from '@/env';
import { zodResolver } from '@hookform/resolvers/zod';
import imageCompression from 'browser-image-compression';
import { useAtom } from 'jotai';
import { ImageIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import useMeasure from 'react-use-measure';
import { z } from 'zod';

import type { Conversation } from '@willow/chat';
import { Messages, useChat, useSendMessage, useUploadFile } from '@willow/chat';
import { Button } from '@willow/ui/base/button';
import { DialogContent } from '@willow/ui/base/dialog';
import { Form, FormControl, FormField, FormItem } from '@willow/ui/base/form';
import { Loader } from '@willow/ui/loader';
import { accessToken<PERSON>tom } from '@willow/utils/api/auth';
import { formatFilename } from '@willow/utils/format';
import { useInvalidatedQuery } from '@willow/utils/react-query';

import { ChatInput } from '~/components/ui/chat-input';
import { useCurrentDoctor } from '~/hooks/doctor';
import { useCurrentPatient } from '~/hooks/patient';

const schema = z.object({
  message: z.string().min(1),
  needsReply: z.boolean().optional(),
});

export const Chat = () => {
  const doctor = useCurrentDoctor();
  const { messages, isLoading: chatLoading, conversation } = useChat();

  const [signIn] = useAtom(accessTokenAtom);

  const isLoading = chatLoading;

  if (!conversation || !signIn) return null;

  return (
    <div className="flex h-full min-w-0 flex-col">
      <div className="flex min-w-0 flex-1 flex-col gap-6 overflow-y-scroll px-8 pt-4">
        {isLoading && <Loader className="h-full" size="md" />}
        {!isLoading && messages.length === 0 && <EmptyChat />}
        {!isLoading && messages.length > 0 && (
          <Messages
            messages={messages}
            participants={conversation.participants}
            currentUserId={doctor.userId}
            isLoading={isLoading}
            showPatientServicesNotification={true}
          />
        )}
      </div>
      <div className="w-full gap-2 px-8 pb-4 md:pb-6">
        <MessageInput conversation={conversation} />
      </div>
    </div>
  );
};

const MessageInput = ({ conversation }: { conversation: Conversation }) => {
  const invalidatedQuery = useInvalidatedQuery();
  const { mutateAsync: sendMessage, isPending: isSubmittingMessage } =
    useSendMessage(env.NEXT_PUBLIC_API_URL);
  const { mutateAsync: uploadFile, isPending: isUploadingFile } = useUploadFile(
    env.NEXT_PUBLIC_API_URL,
  );

  const [chatRef, bounds] = useMeasure();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [isSendingImage, setIsSendingImage] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      message: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    if (!conversation) return;

    const { message, needsReply } = data;

    await sendMessage({
      conversationId: conversation.id,
      content: message,
      contentType: 'text',
      needsReply,
    });

    if (needsReply) {
      void invalidatedQuery(['watcher', conversation.id]);
    }
    void invalidatedQuery(['doctor', 'dashboard']);
    form.setValue('message', '');
    inputRef.current?.focus();
  };

  const submitImage = async (image: File) => {
    if (!conversation) return;

    try {
      setIsSendingImage(true);
      const processedImage = await imageCompression(image, {
        maxWidthOrHeight: 1200,
        useWebWorker: true,
        fileType: 'image/jpeg',
        initialQuality: 0.8,
      });

      await uploadFile({
        conversationId: conversation.id,
        filename: image.name,
        file: processedImage,
      });

      await sendMessage({
        conversationId: conversation.id,
        content: image.name,
        contentType: 'image',
        needsReply: false,
      });
      void invalidatedQuery(['doctor', 'dashboard']);
      setIsSendingImage(false);
    } catch (e) {
      setIsSendingImage(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && (event.metaKey || event.ctrlKey)) {
      event.preventDefault();
      void form.handleSubmit(onSubmit)();
    }
  };

  return (
    <div>
      <Form {...form}>
        <form
          onSubmit={(e) => {
            const submitButton = document.activeElement as HTMLButtonElement;
            const needsReply = submitButton.value === 'needs-reply';
            form.setValue('needsReply', needsReply);
            void form.handleSubmit(onSubmit)(e);
          }}
          className="relative flex w-full items-center border-t border-denim bg-white md:border-0"
          style={{ height: bounds.height + 20 }}
        >
          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem className="relative w-full text-xl font-medium">
                <FormControl>
                  <ChatInput
                    autoFocus={true}
                    chatRef={chatRef}
                    {...field}
                    ref={inputRef}
                    placeholder="Message"
                    className="placeholder:text-gray-400 focus-visible:outline-none"
                    onKeyDown={handleKeyDown}
                  />
                </FormControl>

                <div className="absolute right-2 top-0 hover:opacity-80">
                  <input
                    className="hidden"
                    id="upload-image"
                    name="image"
                    type="file"
                    accept="image/*"
                    onChange={(event) => {
                      if (event.target.files) {
                        setSelectedImage(
                          formatFilename(event.target.files[0]!),
                        );
                        event.target.value = '';
                      }
                    }}
                  />

                  <label htmlFor="upload-image" className="cursor-pointer">
                    <ImageIcon className="text-gray-500" />
                  </label>
                </div>
              </FormItem>
            )}
          />

          <div className="flex gap-2">
            <div className="my-15 flex flex-col gap-3 self-end">
              <Button
                size="sm"
                variant="denim"
                className="text-xs text-white"
                type="submit"
                loading={isSubmittingMessage || isUploadingFile}
              >
                SEND MESSAGE
              </Button>
              <Button
                size="sm"
                variant="tertiary"
                className="text-xs text-white"
                type="submit"
                value="needs-reply"
                loading={isSubmittingMessage || isUploadingFile}
              >
                SEND MESSAGE NEEDS REPLY
              </Button>
            </div>
          </div>
        </form>
      </Form>

      {selectedImage && (
        <Dialog open={true} onOpenChange={() => setSelectedImage(null)}>
          <DialogContent className="w-full max-w-[699px]">
            <div className="flex flex-col items-center justify-center gap-10">
              <div className="font-['Neue Haas Grotesk Display Pro'] text-center text-3xl font-medium text-slate-600">
                <Image
                  alt="Upload image"
                  src={URL.createObjectURL(selectedImage)}
                  width={100}
                  height={100}
                  className="h-auto max-h-60 w-full"
                />
              </div>

              <div className="flex gap-5">
                <Button
                  variant="denimOutline"
                  onClick={() => setSelectedImage(null)}
                  disabled={isSendingImage}
                >
                  Cancel
                </Button>
                <Button
                  variant="denim"
                  onClick={async () => {
                    await submitImage(selectedImage);
                    setSelectedImage(null);
                  }}
                  disabled={isSendingImage}
                >
                  Send Image
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

const EmptyChat = () => {
  const patient = useCurrentPatient();

  if (!patient) {
    return <Loader className="h-full" size="md" />;
  }

  const timestamp = new Date().getTime();
  const baseUrl = env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL;

  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-2.5 pb-20">
      <div className="inline-flex h-24 w-24 items-center justify-center gap-2.5 overflow-hidden rounded-full">
        <Image
          className="h-full w-full"
          alt="Profile image"
          src={`${baseUrl}/${patient.facePhoto}?${timestamp}`}
          width={100}
          height={100}
          style={{ objectFit: 'cover' }}
        />
      </div>

      <div className="flex flex-col items-center justify-center">
        <div className="text-xl font-semibold text-denim">
          {patient.user.firstName} {patient.user.lastName}
        </div>
        <div className="text-center text-xl font-medium text-denim text-opacity-60">
          Your Patient
        </div>
      </div>
    </div>
  );
};
