import { NestFactory, Reflector } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';

import { AppModule } from './app.module';

import './tracer'; // must come before importing any instrumented module.

import {
  ClassSerializerInterceptor,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as cookieParser from 'cookie-parser';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    rawBody: true,
    bufferLogs: true,
  });

  if (process.env.ENVIRONMENT !== 'local') app.useLogger(app.get(Logger));

  app.useBodyParser('text');

  // @todo configure CORS properly
  app.enableCors();

  //setup cookies with encryption
  app.use(cookieParser(process.env.APP_ENCRYPTION_KEY));

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
  });

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Intercept all responses and serialize the response body
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  const config = new DocumentBuilder()
    .setTitle('Willow API')
    .setDescription('The Willow API description')
    .setVersion('1.0')
    .addTag('willow')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const host = process.env.API_HOST ?? '0.0.0.0';
  const port = +(process.env.API_PORT ?? 8080);
  console.log(`Listening on ${host}:${port}`);
  await app.listen(port, host);
}

bootstrap().catch((error) => {
  console.error(error);
  setTimeout(() => {
    process.exit(1);
  }, 5_000); // Wait for 5 seconds before exiting to allow logs to flush
});
