import { DoctorPersistence } from '@/adapters/persistence/database/doctor.persistence';
import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { QuestionnairePersistence } from '@/adapters/persistence/database/questionnaire.persistence';
import { PrismaModule } from '@/modules/prisma/prisma.module';
import { AiModule } from '@modules/ai/ai.module';
import { AuthModule } from '@modules/auth/auth.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { DosespotModule } from '@modules/dosespot/dosespot.module';
import { FollowUpUseCases } from '@modules/follow-up/use-cases';
import { StripeModule } from '@modules/stripe/stripe.module';
import { forwardRef, Module } from '@nestjs/common';

import { AuditLogModule } from '../audit-log/audit-log.module';
import { ContextModule } from '../context/context.module';
import { DoctorModule } from '../doctor/doctor.module';
import { IntercomModule } from '../intercom/intercom.module';
import { PatientModule } from '../patient/patient.module';
import { PatientService } from '../patient/patient.service';
import { SnsModule } from '../shared/aws/sns/sns.module';
import { OrchestrationModule } from '../shared/orchestration/orchestration.module';
import { OutboxerModule } from '../shared/outboxer/outboxer.module';
import { SharedModule } from '../shared/shared.module';
import { TreatmentService } from '../treatment/services/treatment.service';
import { FollowUpTreatmentConsumer } from './consumers/follow-up-treatment.consumer';
import { FollowUpConsumer } from './consumers/follow-up.consumer';
import { FollowUpController } from './follow-up.controller';
import { FollowUpWorker } from './follow-up.worker';
import { SendFollowUpNotificationsJob } from './jobs/send-follow-up-notifications.job';
import { FollowUpStateService } from './services/follow-up-state.service';
import { FollowUpService } from './services/follow-up.service';

@Module({
  imports: [
    IntercomModule,
    PrismaModule,
    forwardRef(() => AuthModule),
    forwardRef(() => SharedModule),
    DosespotModule,
    PatientModule,
    DoctorModule,
    StripeModule,
    AiModule,
    AuditLogModule,
    AppCacheModule,
    OutboxerModule,
    SnsModule,
    ContextModule,
    OrchestrationModule,
  ],
  providers: [
    FollowUpWorker,
    TreatmentService,
    FollowUpService,
    QuestionnairePersistence,
    DoctorPersistence,
    PatientPersistence,
    PatientService,
    FollowUpStateService,
    SendFollowUpNotificationsJob,
    PatientPaymentMethodPersistence,
    FollowUpTreatmentConsumer,
    FollowUpConsumer,
    ...FollowUpUseCases,
  ],
  controllers: [FollowUpController],
  exports: [FollowUpService],
})
export class FollowUpModule {}
