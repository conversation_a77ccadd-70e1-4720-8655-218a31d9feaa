import { StripeService } from '@/modules/stripe/service/stripe.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { addDays, subDays } from 'date-fns';

export type LilyDirectSubscriptionInvoiceMetadata = {
  subscriptionName: typeof LilyDirectService.NAME;
  patientId: string;
  subscriptionStartDate: string;
  subscriptionEndDate: string;
};

@Injectable()
export class LilyDirectService {
  private readonly logger = new Logger(LilyDirectService.name);

  static readonly NAME = 'LilyDirect';
  static readonly PRICE_IN_CENTS = 9900;
  static readonly validForPeriodInDays = 29;
  static readonly rechargePeriodIndDays = 28;

  constructor(
    private readonly prisma: PrismaService,
    private readonly stripe: StripeService,
  ) {}

  async hasActiveSubscription(data: { patientId: string }) {
    const subscription = await this.prisma.subscription.findFirst({
      where: { patientId: data.patientId, name: LilyDirectService.NAME },
    });

    if (!subscription || !subscription.endDate) {
      return false;
    }

    const now = new Date();

    return now.getTime() <= subscription.endDate.getTime()
      ? subscription
      : false;
  }

  async chargeForSubscription({
    patientId,
    forPeriod,
  }: {
    patientId: string;
    forPeriod: { startDate: Date; endDate: Date };
  }) {
    const patient = await this.prisma.patient.findUnique({
      where: { id: patientId },
    });

    if (!patient) {
      throw new HttpException('Patient not found', 404);
    }

    if (!patient.stripeCustomerId) {
      throw new HttpException('Patient stripe customer not found', 500);
    }

    const hasSubscription = await this.hasActiveSubscription({
      patientId: patient.id,
    });
    if (hasSubscription) {
      this.logger.debug(
        `Skipping Charging: Patient ${patient.id} already has an active subscription`,
      );
      return;
    }

    // check if invoice already exists
    const now = new Date();
    const pastPeriod = subDays(now, LilyDirectService.rechargePeriodIndDays);

    const result = await this.stripe.listInvoices({
      customerId: patient.stripeCustomerId,
      limit: 1,
      metadata: {
        subscriptionName: {
          equals: LilyDirectService.NAME,
        },
      },
      fromDate: pastPeriod,
      toDate: now,
    });

    const invoice = result.data.at(0);

    if (invoice && (invoice.status === 'paid' || invoice.status === 'open')) {
      this.logger.debug(
        `Skipping Charging: Patient ${patient.id} already has already paid a stripe invoice`,
      );
      return invoice;
    }

    return this.sendInvoice({
      patientId: patient.id,
      stripeCustomerId: patient.stripeCustomerId,
      forPeriod,
    });
  }

  public async sendInvoice({
    patientId,
    stripeCustomerId,
    forPeriod,
  }: {
    patientId: string;
    stripeCustomerId: string;
    forPeriod: { startDate: Date; endDate: Date };
  }) {
    // const draftInvoice = await this.stripe.createInvoice(stripeCustomerId, {
    //   description: 'Willow Platform fee',
    //   metadata: {
    //     patientId: patientId,
    //     subscriptionName: LilyDirectService.NAME,
    //     subscriptionStartDate: forPeriod.startDate.toISOString(),
    //     subscriptionEndDate: forPeriod.endDate.toISOString(),
    //   } satisfies LilyDirectSubscriptionInvoiceMetadata,
    // });
    // await this.stripe.addInvoiceItem({
    //   invoiceId: draftInvoice.id,
    //   customerId: stripeCustomerId,
    //   item: {
    //     type: 'other',
    //     description: 'Platform fee',
    //     amountInCents: LilyDirectService.PRICE_IN_CENTS,
    //   },
    // });
    // await this.stripe.attemptInvoiceCollect(draftInvoice.id);
  }
}
