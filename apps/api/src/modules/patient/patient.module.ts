import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { StatePersistence } from '@adapters/persistence/database/state.persistence';
import { AdminModule } from '@modules/admin/admin.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { ChatModule } from '@modules/chat/chat.module';
import { DoctorModule } from '@modules/doctor/doctor.module';
import { GetPatientPreSignedUrlUseCase } from '@modules/doctor/use-cases/get-patient-pre-signed-url.use-case';
import { UpdatePatientPhotoUseCase as DoctorUpdatePatientPhotoUseCase } from '@modules/doctor/use-cases/update-patient-photo.use-case';
import { DosespotModule } from '@modules/dosespot/dosespot.module';
import { OnboardingModule } from '@modules/onboarding/onboarding.module';
import { PatientAdminUseCases } from '@modules/patient/admin-use-cases';
import { PatientUseCases } from '@modules/patient/use-cases';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { UtmService } from '@modules/shared/services/utm.service';
import { GetStatesUseCase } from '@modules/shared/use-cases/states/get-states.use-case.service';
import { StripeModule } from '@modules/stripe/stripe.module';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { forwardRef, Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { AiModule } from '../ai/ai.module';
import { AiService } from '../ai/ai.service';
import { AuditLogModule } from '../audit-log/audit-log.module';
import { AuthModule } from '../auth/auth.module';
import { ContextModule } from '../context/context.module';
import { IntercomModule } from '../intercom/intercom.module';
import { PharmacyModule } from '../pharmacy/pharmacy.module';
import { ReferralModule } from '../referral/referral.module';
import { SnsModule } from '../shared/aws/sns/sns.module';
import { SqsModule } from '../shared/aws/sqs/sqs.module';
import { OrchestrationModule } from '../shared/orchestration/orchestration.module';
import { OutboxerModule } from '../shared/outboxer/outboxer.module';
import { QuestionnairService } from '../shared/questionnaire/questionnaire.service';
import { SharedModule } from '../shared/shared.module';
import { StateModule } from '../state/state.module';
import { StateService } from '../state/state.service';
import { PatientAdminController } from './patient-admin.controller';
import { PatientAuthController } from './patient-auth.controller';
import { PatientTransferConsumer } from './patient-transfer.consumer';
import { PatientTransferService } from './patient-transfer.service';
import { PatientController } from './patient.controller';
import { PatientService } from './patient.service';
import { PatientRestoreSubscriptionUseCase } from './use-cases/patient-restore-subscription-use-case';
import { PatientUpdatePhotoUseCase } from './use-cases/patient-update-photo.use-case';

@Module({
  imports: [
    IntercomModule,
    AiModule,
    forwardRef(() => AuthModule),
    forwardRef(() => SharedModule),
    forwardRef(() => OnboardingModule),
    forwardRef(() => AdminModule),
    forwardRef(() => DoctorModule), // Important: This must be a forwardRef due to circular dependency
    AuditLogModule,
    DosespotModule,
    forwardRef(() => ChatModule),
    forwardRef(() => PharmacyModule),
    PrismaModule,
    StripeModule,
    forwardRef(() => TreatmentModule),
    ReferralModule,
    AppCacheModule,
    ContextModule,
    StateModule,
    OutboxerModule,
    SnsModule,
    SqsModule,
    OrchestrationModule,
    ScheduleModule.forRoot(), // Required for cron jobs
  ],
  controllers: [
    PatientController,
    PatientAuthController,
    PatientAdminController,
  ],
  providers: [
    AiService,
    GetStatesUseCase,
    StatePersistence,
    PatientPersistence,
    PatientService,
    PatientTransferService,
    PatientTransferConsumer,
    UtmService,
    QuestionnairService,
    PatientPaymentMethodPersistence,
    GetPatientPreSignedUrlUseCase,
    DoctorUpdatePatientPhotoUseCase,
    StateService,
    ...PatientUseCases,
    ...PatientAdminUseCases,
  ],
  exports: [
    PatientRestoreSubscriptionUseCase,
    PatientService,
    PatientTransferService,
    PatientUpdatePhotoUseCase,
  ],
})
export class PatientModule {}
