import { PrismaService } from '@/modules/prisma/prisma.service';
import { SqsService } from '@modules/shared/aws/sqs/sqs.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { bulkTransferStatus } from '@prisma/client';

import { OrchestrationService } from '../shared/orchestration/orchestration.service';
import {
  TransferPatientsDto,
  UpdateBulkTransferDto,
} from './dto/transfer-patients.dto';

interface TransferQueueItem {
  targetDoctorId: string;
  patientId: string;
  reason: string;
  bulkTransferId?: string;
}

@Injectable()
export class PatientTransferService {
  private readonly logger = new Logger(PatientTransferService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly sqsService: SqsService,
    private readonly orchestrationService: OrchestrationService,
  ) {
    this.logger.log('PatientTransferService initialized with cron jobs');
  }

  /**
   * Transfer patients to doctors based on the specified transfer blocks
   * If transferAt or revertAt are provided, the transfer will be scheduled
   */
  async transferToDoctors(dto: TransferPatientsDto): Promise<{
    queuedTransfers: number;
    statesCovered: string[];
    errors: { message: string; details?: any }[];
    bulkTransferId?: string;
  }> {
    const errors: { message: string; details?: any }[] = [];

    // Validate transfer blocks exist
    if (!dto.transferBlocks || dto.transferBlocks.length === 0) {
      errors.push({
        message: 'At least one transfer block is required',
      });
      return { queuedTransfers: 0, statesCovered: [], errors };
    }

    // Validate each transfer block has at least one state and target doctor
    for (const [index, block] of dto.transferBlocks.entries()) {
      if (!block.stateIds || block.stateIds.length === 0) {
        errors.push({
          message: `Transfer block ${index + 1} must include at least one state ID`,
        });
      }

      if (!block.targetDoctorIds || block.targetDoctorIds.length === 0) {
        errors.push({
          message: `Transfer block ${index + 1} must include at least one target doctor ID`,
        });
      }
    }

    // If this is an OOO transfer, validate that revertAt is in the future
    if (dto.revertAt && new Date(dto.revertAt) <= new Date()) {
      errors.push({
        message: 'The revert date must be in the future',
      });
    }

    // If transferAt is provided and it's in the past, log a warning but don't treat as error
    // since we want to support immediate transfers
    if (dto.transferAt && new Date(dto.transferAt) <= new Date()) {
      this.logger.log(
        'Transfer date is in the past or present - treating as immediate transfer',
      );
    }

    // If there are validation errors from the initial checks, return them before proceeding
    if (errors.length > 0) {
      return { queuedTransfers: 0, statesCovered: [], errors };
    }

    // Set default reason if not provided
    const reason = dto.reason || 'Doctor reassignment';

    // Validate the source doctor exists
    const sourceDoctor = await this.prisma.doctor.findUnique({
      where: { id: dto.sourceDoctorId },
      include: {
        user: { select: { firstName: true, lastName: true } },
        prescribesIn: {
          include: { state: true },
        },
      },
    });

    if (!sourceDoctor) {
      errors.push({
        message: `Source doctor with ID ${dto.sourceDoctorId} not found`,
      });
      return { queuedTransfers: 0, statesCovered: [], errors };
    }

    // Get all states the source doctor has patients in
    const doctorPatientStates = await this.prisma.state.findMany({
      where: {
        patients: {
          some: { doctorId: dto.sourceDoctorId },
        },
      },
      select: { id: true, code: true, name: true },
    });

    if (doctorPatientStates.length === 0) {
      errors.push({
        message: `Doctor ${sourceDoctor.user.firstName} ${sourceDoctor.user.lastName} has no patients to transfer`,
      });
      return { queuedTransfers: 0, statesCovered: [], errors };
    }

    // Create a set of all states the doctor has patients in for validation purposes
    const allDoctorPatientStateIds = new Set(
      doctorPatientStates.map((s) => s.id),
    );

    // Create a set to track which states are covered in the transfer blocks
    const coveredStateIds = new Set<string>();

    // Validate that all transfer blocks mention valid states where the doctor has patients
    for (const [blockIndex, block] of dto.transferBlocks.entries()) {
      // Validate state IDs in this block
      for (const stateId of block.stateIds) {
        if (!allDoctorPatientStateIds.has(stateId)) {
          errors.push({
            message: `State ID ${stateId} in transfer block ${blockIndex + 1} is invalid or doctor has no patients in this state`,
          });
        } else {
          // Mark this state as covered
          coveredStateIds.add(stateId);
        }
      }
    }

    // Check if any states with patients haven't been included in transfer blocks
    // but don't treat this as an error - it's a partial transfer
    const missingStateIds = Array.from(allDoctorPatientStateIds).filter(
      (stateId) => !coveredStateIds.has(stateId),
    );

    // Log the partial transfer but don't add an error
    if (missingStateIds.length > 0) {
      const missingStates = doctorPatientStates
        .filter((state) => missingStateIds.includes(state.id))
        .map((state) => `${state.name} (${state.code})`)
        .join(', ');

      this.logger.log(
        `Partial transfer: ${missingStateIds.length} states not included in transfer blocks: ${missingStates}`,
      );
    }

    // If there are validation errors, return them before proceeding
    if (errors.length > 0) {
      return {
        queuedTransfers: 0,
        statesCovered: Array.from(coveredStateIds),
        errors,
      };
    }

    // Determine if this is an out-of-office transfer
    const isOOO = !!dto.revertAt;

    // Always use the cron job for transfers by setting status to PENDING
    // For immediate transfers, set transferAt to current time
    const now = new Date();
    const transferAt = dto.transferAt || now;

    // Create a DoctorBulkTransfer record to track this operation
    const bulkTransfer = await this.prisma.bulkTransfer.create({
      data: {
        doctorId: dto.sourceDoctorId,
        reason: reason,
        rules: dto.transferBlocks,
        transferAt: transferAt,
        revertAt: dto.revertAt,
        status: bulkTransferStatus.pending,
        type: 'doctor',
      },
    });

    // Log the scheduled transfer
    const isImmediate = !dto.transferAt || new Date(dto.transferAt) <= now;
    this.logger.log(
      `${isImmediate ? 'Immediate' : 'Scheduled'} bulk transfer ${bulkTransfer.id} queued for ${transferAt}${
        isOOO ? ` with reversion at ${dto.revertAt}` : ''
      }`,
    );

    return {
      queuedTransfers: 0,
      statesCovered: Array.from(coveredStateIds),
      errors,
      bulkTransferId: bulkTransfer.id,
    };
  }

  /**
   * Process transfer blocks and queue patient transfers
   */
  private async processTransferBlocks(
    dto: TransferPatientsDto,
    bulkTransferId: string,
    reason: string,
    doctorPatientStates: { id: string; code: string; name: string }[],
    coveredStateIds: Set<string>,
  ): Promise<{
    queuedTransfers: number;
    statesCovered: string[];
    errors: { message: string; details?: any }[];
  }> {
    const errors: { message: string; details?: any }[] = [];
    const transferQueue: TransferQueueItem[] = [];

    // Process each transfer block
    for (const [blockIndex, block] of dto.transferBlocks.entries()) {
      // Get target doctors and validate they exist and can prescribe in the specified states
      const targetDoctors = await this.prisma.doctor.findMany({
        where: {
          id: { in: block.targetDoctorIds },
          active: true,
        },
        include: {
          user: { select: { firstName: true, lastName: true, email: true } },
          prescribesIn: {
            select: { stateId: true },
          },
        },
      });

      if (targetDoctors.length !== block.targetDoctorIds.length) {
        const foundIds = targetDoctors.map((d) => d.id);
        const missingIds = block.targetDoctorIds.filter(
          (id) => !foundIds.includes(id),
        );
        errors.push({
          message: `Some target doctors in block ${blockIndex + 1} not found or not active`,
          details: { missingIds },
        });

        if (targetDoctors.length === 0) {
          this.logger.warn(
            `Skipping block ${blockIndex + 1} - no valid target doctors`,
          );
          continue;
        }
      }

      // Create a map of state assignments for each doctor
      const doctorStateMap: Record<string, Set<string>> = {};

      for (const doctor of targetDoctors) {
        doctorStateMap[doctor.id] = new Set(
          doctor.prescribesIn.map((state) => state.stateId),
        );
      }

      // Validate that each target doctor can prescribe in the specified states
      for (const stateId of block.stateIds) {
        const eligibleDoctors = targetDoctors.filter((doctor) =>
          doctor.prescribesIn.some((state) => state.stateId === stateId),
        );

        if (eligibleDoctors.length === 0) {
          const state = doctorPatientStates.find((s) => s.id === stateId);
          errors.push({
            message: `No target doctors in block ${blockIndex + 1} can prescribe in state ${state?.name || stateId}`,
            details: { blockIndex, stateId },
          });
        }
      }

      // Get patients for this block
      const patients = await this.prisma.patient.findMany({
        where: {
          doctorId: dto.sourceDoctorId,
          stateId: { in: block.stateIds },
        },
        select: {
          id: true,
          stateId: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (patients.length === 0) {
        this.logger.warn(`No patients found for block ${blockIndex + 1}`);
        continue;
      }

      this.logger.log(
        `Processing ${patients.length} patients for block ${blockIndex + 1}`,
      );

      // Group patients by state ID
      const patientsByState: Record<string, typeof patients> = {};

      for (const patient of patients) {
        if (!patient.stateId) continue;

        if (!patientsByState[patient.stateId]) {
          patientsByState[patient.stateId] = [];
        }

        patientsByState[patient.stateId].push(patient);
      }

      // For each state, distribute patients among eligible doctors
      for (const stateId in patientsByState) {
        const statePatients = patientsByState[stateId];

        // Find doctors eligible for this state
        const eligibleDoctorIds = targetDoctors
          .filter((doctor) => doctorStateMap[doctor.id].has(stateId))
          .map((doctor) => doctor.id);

        if (eligibleDoctorIds.length === 0) {
          // Skip - already logged an error above
          continue;
        }

        // Distribute patients evenly among eligible doctors
        for (let i = 0; i < statePatients.length; i++) {
          const patient = statePatients[i];
          // Simple round-robin distribution
          const doctorIndex = i % eligibleDoctorIds.length;
          const targetDoctorId = eligibleDoctorIds[doctorIndex];

          transferQueue.push({
            targetDoctorId,
            patientId: patient.id,
            reason,
            bulkTransferId: bulkTransferId,
          });
        }
      }
    }

    if (transferQueue.length === 0) {
      errors.push({
        message: 'No patient transfers to queue after processing all blocks',
      });
      return {
        queuedTransfers: 0,
        statesCovered: Array.from(coveredStateIds),
        errors,
      };
    }

    // Queue all transfers using SQS
    const queueName = 'patient-transfers';
    const queueUrl = await this.sqsService.ensureQueueExists({
      queueName,
      deadLetterQueueName: 'patient-transfers-dlq',
    });

    this.logger.log(`Using SQS queue: ${queueUrl} for patient transfers`);

    // Process transfers in batches of 10 (AWS SQS batch limit)
    const BATCH_SIZE = 10;
    let queuedCount = 0;

    for (let i = 0; i < transferQueue.length; i += BATCH_SIZE) {
      const batch = transferQueue.slice(i, i + BATCH_SIZE);

      // Prepare batch messages for SQS
      const batchMessages = batch.map((transfer, index) => {
        // Create a valid batch entry ID (alphanumeric, hyphens, underscores, max 80 chars)
        // Use index to ensure uniqueness within the batch
        const batchId =
          `transfer-${transfer.patientId.substring(0, 8)}-${index}-${Date.now()}`.substring(
            0,
            80,
          );

        return {
          id: batchId,
          message: {
            metadata: {
              eventId: `transfer-${transfer.patientId}-${transfer.targetDoctorId}-${Date.now()}`,
              timestamp: new Date().toISOString(),
            },
            payload: {
              targetDoctorId: transfer.targetDoctorId,
              patientId: transfer.patientId,
              reason: transfer.reason,
              bulkTransferId: transfer.bulkTransferId,
            },
          },
        };
      });

      // Send batch of messages
      const result = await this.sqsService.sendMessageBatch({
        queueName,
        messages: batchMessages,
      });

      // Count successful messages
      queuedCount += result.successful.length;

      // Log any failures
      if (result.failed.length > 0) {
        this.logger.error(
          `Failed to queue ${result.failed.length} transfers for bulk transfer ${bulkTransferId}`,
        );
      }

      // Add a small delay between batches
      if (i + BATCH_SIZE < transferQueue.length) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    // Update the bulk transfer record with the number of queued jobs
    if (bulkTransferId && queuedCount > 0) {
      await this.prisma.bulkTransfer.update({
        where: {
          id: bulkTransferId,
          type: 'doctor',
        },
        data: { queuedJobs: queuedCount },
      });
    }

    return {
      queuedTransfers: queuedCount,
      statesCovered: Array.from(coveredStateIds),
      errors,
    };
  }

  /**
   * Cron job to process pending bulk transfers and cleanup stalled transfers
   * Runs every minute to check for transfers that need to be processed
   */
  @Cron('0 * * * * *')
  async processPendingBulkTransfers(): Promise<void> {
    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'processPendingBulkTransfers-cron',
          ttl: 60 * 1000, // 1 minute
          thisInstanceMustBePrimary: true,
        },
        async () => {
          this.logger.log(
            'Checking for pending bulk transfers and stalled transfers',
          );

          // First, check for stalled transfers that have been in progress for too long (>15 mins)
          const stalledTransfers = await this.prisma.bulkTransfer.findMany({
            where: {
              type: 'doctor',
              status: bulkTransferStatus.inProgress,
              createdAt: {
                lt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
              },
            },
          });

          if (stalledTransfers.length > 0) {
            this.logger.log(
              `Found ${stalledTransfers.length} stalled bulk transfers to clean up`,
            );

            for (const transfer of stalledTransfers) {
              try {
                this.logger.warn(
                  `Bulk transfer ${transfer.id} stalled after ${Math.floor(
                    (Date.now() - transfer.createdAt.getTime()) / 60000,
                  )} minutes - marking as completed`,
                );

                await this.prisma.bulkTransfer.update({
                  where: {
                    id: transfer.id,
                    type: 'doctor',
                  },
                  data: {
                    status: bulkTransferStatus.completed,
                    completedAt: new Date(),
                  },
                });
              } catch (error) {
                this.logger.error(
                  `Error cleaning up stalled transfer ${transfer.id}:`,
                  error.stack,
                );
              }
            }
          }

          // Then, find bulk transfers that are scheduled to be executed now or in the past
          const pendingTransfers = await this.prisma.bulkTransfer.findMany({
            where: {
              type: 'doctor',
              status: bulkTransferStatus.pending,
              transferAt: {
                lte: new Date(),
              },
            },
            orderBy: {
              transferAt: 'asc',
            },
            take: 5, // Process a few at a time to avoid overwhelming the system
          });

          if (pendingTransfers.length === 0) return;

          this.logger.log(
            `Processing ${pendingTransfers.length} pending bulk transfers`,
          );

          for (const transfer of pendingTransfers) {
            try {
              // Check if another job is already processing this transfer (in case of concurrent execution)
              const currentTransfer = await this.prisma.bulkTransfer.findUnique(
                {
                  where: {
                    id: transfer.id,
                    type: 'doctor',
                  },
                },
              );

              if (currentTransfer.status !== bulkTransferStatus.pending) {
                this.logger.log(
                  `Skipping bulk transfer ${transfer.id} as it's already being processed (status: ${currentTransfer.status})`,
                );
                continue;
              }

              // Update status to in progress
              await this.prisma.bulkTransfer.update({
                where: {
                  id: transfer.id,
                  type: 'doctor',
                },
                data: { status: bulkTransferStatus.inProgress },
              });

              // Create a DTO from the stored rules
              const dto: TransferPatientsDto = {
                sourceDoctorId: transfer.doctorId,
                reason: transfer.reason || undefined,
                transferBlocks: transfer.rules as any, // The rules are stored as JSON
                transferAt: transfer.transferAt,
                revertAt: transfer.revertAt,
              };

              // Get necessary data to process the transfer
              const doctorPatientStates = await this.prisma.state.findMany({
                where: {
                  patients: {
                    some: { doctorId: transfer.doctorId },
                  },
                },
                select: { id: true, code: true, name: true },
              });

              const coveredStateIds = new Set<string>();
              for (const block of dto.transferBlocks) {
                for (const stateId of block.stateIds) {
                  coveredStateIds.add(stateId);
                }
              }

              // Process the transfer and queue all the individual patient transfers
              const transferResult = await this.processTransferBlocks(
                dto,
                transfer.id,
                transfer.reason || 'Scheduled transfer',
                doctorPatientStates,
                coveredStateIds,
              );

              if (transferResult.errors.length > 0) {
                this.logger.warn(
                  `Bulk transfer ${transfer.id} queued with ${transferResult.errors.length} errors: ${JSON.stringify(transferResult.errors)}`,
                );
              }

              // We keep the status as IN_PROGRESS since the actual transfers
              // will complete asynchronously via SQS
              // The status will be updated to COMPLETED when all patient transfers are done

              this.logger.log(
                `Successfully queued ${transferResult.queuedTransfers} transfers for bulk transfer ${transfer.id}`,
              );
            } catch (error) {
              this.logger.error(
                `Error processing bulk transfer ${transfer.id}:`,
                error.stack,
              );

              // Update status to failed and set completedAt
              await this.prisma.bulkTransfer.update({
                where: {
                  id: transfer.id,
                  type: 'doctor',
                },
                data: {
                  status: bulkTransferStatus.failed,
                  reason: `${transfer.reason || 'Scheduled transfer'} (Failed: ${error.message})`,
                  completedAt: new Date(),
                },
              });
            }
          }
        },
      );
    } catch (error) {
      this.logger.error(
        'Error in processPendingBulkTransfers cron job:',
        error,
      );
    }
  }

  /**
   * Cron job to process bulk transfer reversions
   * Runs every minute to check for transfers that need to be reverted
   */
  @Cron('30 */5 * * * *') // Run 30 seconds after the main job
  async processBulkTransferReversions(): Promise<void> {
    try {
      return this.orchestrationService.runWithLock(
        {
          lockKey: 'processBulkTransferReversions-cron',
          ttl: 60 * 1000, // 1 minute
          thisInstanceMustBePrimary: true,
        },
        async () => {
          try {
            // First, check for stalled reversions (in REVERTING status for > 15 minutes)
            const stalledReversions = await this.prisma.bulkTransfer.findMany({
              where: {
                type: 'doctor',
                status: bulkTransferStatus.reverting,
                createdAt: {
                  lt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
                },
              },
            });

            if (stalledReversions.length > 0) {
              this.logger.log(
                `Found ${stalledReversions.length} stalled bulk transfer reversions to clean up`,
              );

              for (const transfer of stalledReversions) {
                try {
                  this.logger.warn(
                    `Bulk transfer reversion ${transfer.id} stalled after ${Math.floor(
                      (Date.now() - transfer.createdAt.getTime()) / 60000,
                    )} minutes - marking as reverted`,
                  );

                  await this.prisma.bulkTransfer.update({
                    where: {
                      id: transfer.id,
                      type: 'doctor',
                    },
                    data: {
                      status: bulkTransferStatus.reverted,
                      completedAt: new Date(),
                    },
                  });
                } catch (error) {
                  this.logger.error(
                    `Error cleaning up stalled reversion ${transfer.id}:`,
                    error.stack,
                  );
                }
              }
            }

            // Find completed bulk transfers with revertAt in the past
            const transfersToRevert = await this.prisma.bulkTransfer.findMany({
              where: {
                type: 'doctor',
                status: bulkTransferStatus.completed,
                revertAt: {
                  not: null,
                  lte: new Date(),
                },
              },
              orderBy: {
                revertAt: 'asc',
              },
              take: 5, // Process a few at a time
            });

            if (transfersToRevert.length === 0) return;

            this.logger.log(
              `Processing ${transfersToRevert.length} bulk transfer reversions`,
            );

            for (const transfer of transfersToRevert) {
              try {
                // Check if another job is already processing this transfer
                const currentTransfer =
                  await this.prisma.bulkTransfer.findUnique({
                    where: {
                      id: transfer.id,
                      type: 'doctor',
                    },
                  });

                if (currentTransfer.status !== bulkTransferStatus.completed) {
                  this.logger.log(
                    `Skipping bulk transfer reversion ${transfer.id} as it's not in COMPLETED state (status: ${currentTransfer.status})`,
                  );
                  continue;
                }

                await this.revertBulkTransfer(transfer.id);
                this.logger.log(
                  `Successfully reverted bulk transfer ${transfer.id}`,
                );
              } catch (error) {
                this.logger.error(
                  `Error reverting bulk transfer ${transfer.id}:`,
                  error.stack,
                );
              }
            }
          } catch (error) {
            this.logger.error(
              'Error in processBulkTransferReversions job:',
              error.stack,
            );
            throw error;
          }
        },
      );
    } catch (error) {
      this.logger.error(
        'Error in processBulkTransferReversions cron job:',
        error,
      );
    }
  }

  /**
   * Revert a bulk transfer - moves patients back to their original doctor
   */
  private async revertBulkTransfer(bulkTransferId: string): Promise<void> {
    // Get the bulk transfer record
    const bulkTransfer = await this.prisma.bulkTransfer.findUnique({
      where: {
        id: bulkTransferId,
        type: 'doctor',
      },
      include: {
        doctor: true,
      },
    });

    if (!bulkTransfer) {
      throw new Error(`Bulk transfer ${bulkTransferId} not found`);
    }

    if (bulkTransfer.status === bulkTransferStatus.reverted) {
      this.logger.log(`Bulk transfer ${bulkTransferId} already reverted`);
      return;
    }

    // Update status to REVERTING and reset job counters
    await this.prisma.bulkTransfer.update({
      where: {
        id: bulkTransferId,
        type: 'doctor',
      },
      data: {
        status: bulkTransferStatus.reverting,
        queuedJobs: 0, // We'll set this to actual count after getting patient IDs
        completedJobs: 0,
      },
    });

    // Get all doctor assignments created by this bulk transfer
    const originalAssignments = await this.prisma.doctorAssignment.findMany({
      where: { bulkTransferId: bulkTransferId },
      include: {
        patient: true,
      },
    });

    if (originalAssignments.length === 0) {
      this.logger.warn(
        `No doctor assignments found for bulk transfer ${bulkTransferId}`,
      );
      await this.prisma.bulkTransfer.update({
        where: {
          id: bulkTransferId,
          type: 'doctor',
        },
        data: { status: bulkTransferStatus.reverted },
      });
      return;
    }

    const originalDoctorId = bulkTransfer.doctorId;
    const reason = `Reversion of OOO transfer: ${bulkTransfer.reason || 'Out of office'}`;

    // Create a set of patient IDs for this bulk transfer
    const patientIds = Array.from(
      new Set(originalAssignments.map((a) => a.patientId)),
    );
    const totalReversions = patientIds.length;

    // Update the queuedJobs count now that we know how many patients to revert
    await this.prisma.bulkTransfer.update({
      where: {
        id: bulkTransferId,
        type: 'doctor',
      },
      data: { queuedJobs: totalReversions },
    });

    // Queue all reversions using SQS, same as normal transfers
    const queueName = 'patient-transfers';
    const queueUrl = await this.sqsService.ensureQueueExists({
      queueName,
      deadLetterQueueName: 'patient-transfers-dlq',
    });

    this.logger.log(
      `Using SQS queue: ${queueUrl} for patient transfer reversions`,
    );

    // Process reversions in batches of 10 to respect rate limits
    const BATCH_SIZE = 10;
    let queuedCount = 0;
    const transferQueue: TransferQueueItem[] = [];

    // Prepare the queue items
    for (const patientId of patientIds) {
      transferQueue.push({
        targetDoctorId: originalDoctorId,
        patientId,
        reason,
        bulkTransferId,
      });
    }

    // Queue the reversions in batches
    for (let i = 0; i < transferQueue.length; i += BATCH_SIZE) {
      const batch = transferQueue.slice(i, i + BATCH_SIZE);

      // Prepare batch messages for SQS
      const batchMessages = batch.map((transfer, index) => {
        const batchId =
          `revert-${transfer.patientId.substring(0, 8)}-${index}-${Date.now()}`.substring(
            0,
            80,
          );
        return {
          id: batchId,
          message: {
            metadata: {
              eventId: `revert-${transfer.patientId}-${transfer.targetDoctorId}-${Date.now()}`,
              timestamp: new Date().toISOString(),
            },
            payload: {
              targetDoctorId: transfer.targetDoctorId,
              patientId: transfer.patientId,
              reason: transfer.reason,
              bulkTransferId: transfer.bulkTransferId,
              isReversion: true, // Flag to indicate this is a reversion
            },
          },
        };
      });

      // Send batch of messages
      const result = await this.sqsService.sendMessageBatch({
        queueName,
        messages: batchMessages,
      });

      // Count successful messages
      queuedCount += result.successful.length;

      // Log any failures
      if (result.failed.length > 0) {
        this.logger.error(
          `Failed to queue ${result.failed.length} reversions for bulk transfer ${bulkTransferId}`,
        );
      }

      // Add a small delay between batches
      if (i + BATCH_SIZE < transferQueue.length) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    this.logger.log(
      `Queued ${queuedCount} reversions for bulk transfer ${bulkTransferId}`,
    );

    // Note: The status will remain REVERTING until all reversions are processed by the SQS consumer
    // which will update completedJobs and eventually set the status to REVERTED
  }

  /**
   * Update an existing bulk transfer
   * @param doctorId - The doctor ID that owns the transfer
   * @param bulkTransferId - The bulk transfer ID to update
   * @param data - The updated transfer data
   * @returns The updated bulk transfer
   * @throws Error if transfer is not found, doesn't belong to the doctor, or is not in pending status
   */
  async updateBulkTransfer(
    doctorId: string,
    bulkTransferId: string,
    data: UpdateBulkTransferDto,
  ) {
    // Get the current bulk transfer
    const bulkTransfer = await this.prisma.bulkTransfer.findUnique({
      where: {
        id: bulkTransferId,
        type: 'doctor',
      },
    });

    if (!bulkTransfer) {
      throw new Error('Bulk transfer not found');
    }

    // Verify that the bulk transfer belongs to the specified doctor
    if (bulkTransfer.doctorId !== doctorId) {
      throw new Error('Bulk transfer does not belong to the specified doctor');
    }

    // Only allow updates if the transfer is pending
    if (bulkTransfer.status !== bulkTransferStatus.pending) {
      throw new Error(
        `Bulk transfer cannot be updated when in ${bulkTransfer.status} status`,
      );
    }

    // Prepare update data
    const updateData: any = {};

    // Only include fields that were provided
    if (data.reason !== undefined) updateData.reason = data.reason;

    // Handle both field names - rules and transferBlocks
    if (data.rules !== undefined) {
      updateData.rules = data.rules;
    } else if (data.transferBlocks !== undefined) {
      updateData.rules = data.transferBlocks;
    }

    if (data.transferAt !== undefined) updateData.transferAt = data.transferAt;
    if (data.revertAt !== undefined) updateData.revertAt = data.revertAt;

    // Update the bulk transfer
    return this.prisma.bulkTransfer.update({
      where: {
        id: bulkTransferId,
        type: 'doctor',
      },
      data: updateData,
      include: {
        doctor: {
          include: { user: true },
        },
      },
    });
  }

  /**
   * Delete a bulk transfer for a doctor
   * @param doctorId - The doctor ID
   * @param bulkTransferId - The bulk transfer ID
   * @returns Object indicating success
   * @throws Error if bulk transfer is not found, doesn't belong to the doctor, or is not in 'pending' status
   */
  async deleteBulkTransfer(doctorId: string, bulkTransferId: string) {
    // Get the current bulk transfer
    const bulkTransfer = await this.prisma.bulkTransfer.findUnique({
      where: {
        id: bulkTransferId,
        type: 'doctor',
      },
    });

    if (!bulkTransfer) {
      throw new Error('Bulk transfer not found');
    }

    // Verify that the bulk transfer belongs to the specified doctor
    if (bulkTransfer.doctorId !== doctorId) {
      throw new Error('Bulk transfer does not belong to the specified doctor');
    }

    // Only allow deletion if the transfer is pending
    if (bulkTransfer.status !== bulkTransferStatus.pending) {
      throw new Error(
        `Bulk transfer cannot be deleted when in ${bulkTransfer.status} status`,
      );
    }

    // Delete the bulk transfer
    await this.prisma.bulkTransfer.delete({
      where: {
        id: bulkTransferId,
        type: 'doctor',
      },
    });

    return { success: true };
  }
}
