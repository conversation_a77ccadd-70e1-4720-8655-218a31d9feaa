import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import Redlock, { Lock } from 'redlock';

@Injectable()
export class RedlockService {
  private readonly logger = new Logger(RedlockService.name);
  private redlock: Redlock;
  private redisClient: Redis;
  private keyPrefix: string = '';

  constructor(private configService: ConfigService) {
    this.initRedlock();

    // Get the same prefix that's used in the CacheService
    const worktreeName = this.configService.get<string>('WORKTREE_NAME');
    const environment =
      this.configService.get<string>('ENVIRONMENT') || 'development';

    const prefix = [
      worktreeName ? `worktree_${worktreeName}` : null,
      environment ? `env_${environment}` : null,
    ]
      .filter(Boolean)
      .join(':');

    this.keyPrefix = `redlock:${prefix ? `${prefix}:` : ''}`;
  }

  private initRedlock() {
    try {
      // Create a Redis client
      this.redisClient = new Redis({
        host: this.configService.get<string>('REDIS_HOST'),
        port: parseInt(this.configService.get<string>('REDIS_PORT')),
      });

      // Create a Redlock instance
      this.redlock = new Redlock(
        // You can use multiple Redis clients for higher availability
        [this.redisClient],
        {
          // The expected clock drift; for more details see:
          // http://redis.io/topics/distlock
          driftFactor: 0.01, // multiplied by lock ttl to determine drift time

          // The max number of times Redlock will attempt to lock a resource
          // before erroring
          retryCount: 3,

          // The time in ms between attempts
          retryDelay: 200, // time in ms

          // The max time in ms randomly added to retries
          // to improve performance under high contention
          retryJitter: 200, // time in ms

          // The minimum remaining time on a lock before an extension is automatically
          // attempted with the `using` API
          automaticExtensionThreshold: 500, // time in ms
        },
      );

      // Handle Redlock errors
      this.redlock.on('error', (error) => {
        // Ignore cases where a resource is explicitly marked as locked
        if (error.name !== 'LockError') {
          this.logger.warn(`Redlock error: ${error.message}`);
        }
      });

      this.logger.log('Redlock initialized successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize Redlock: ${error.message}`);
    }
  }

  /**
   * Apply the prefix to a resource if a prefix is set
   * @param resource The original resource
   * @returns The resource with prefix applied if a prefix is set
   */
  private applyPrefix(resource: string): string {
    return this.keyPrefix ? `${this.keyPrefix}:${resource}` : resource;
  }

  /**
   * Acquire a lock using Redlock
   * @param resource The resource to lock
   * @param ttl The time-to-live in milliseconds
   * @returns A Lock object if successful, null otherwise
   */
  async acquireLock(resource: string, ttl: number): Promise<Lock | null> {
    try {
      if (!this.redlock) {
        this.logger.warn('Redlock not initialized');
        return null;
      }

      // Apply prefix to resource
      const prefixedResource = this.applyPrefix(resource);

      // Acquire a lock
      const lock = await this.redlock.acquire([prefixedResource], ttl);
      return lock;
    } catch (error) {
      if (error.name !== 'LockError') {
        this.logger.warn(
          `Error acquiring lock for ${resource}: ${error.message}`,
        );
      }
      return null;
    }
  }

  /**
   * Release a lock
   * @param lock The lock to release
   * @returns true if successful, false otherwise
   */
  async releaseLock(lock: Lock): Promise<boolean> {
    try {
      if (!lock) {
        this.logger.warn('Attempted to release a null lock');
        return false;
      }

      await lock.release();
      return true;
    } catch (error) {
      this.logger.warn(`Error releasing lock: ${error.message}`);
      return false;
    }
  }

  /**
   * Execute a function while holding a lock
   * @param resource The resource to lock
   * @param ttl The time-to-live in milliseconds
   * @param fn The function to execute while holding the lock
   * @returns The result of the function
   */
  async using<T>(
    resource: string,
    ttl: number,
    fn: () => Promise<T>,
  ): Promise<T | null> {
    let lock: Lock | null = null;
    try {
      lock = await this.acquireLock(resource, ttl);
      if (!lock) {
        return null;
      }

      // Execute the function while holding the lock
      return await fn();
    } catch (error) {
      this.logger.error(`Error executing function with lock: ${error.message}`);
      return null;
    } finally {
      // Always release the lock
      if (lock) {
        await this.releaseLock(lock);
      }
    }
  }

  async lockWhileRunning<T>(
    resource: string,
    ttl: number,
    fn: () => Promise<T>,
  ): Promise<T | null> {
    this.logger.debug(`Running function with lock on resource: ${resource}`);
    return this.redlock.using([this.applyPrefix(resource)], ttl, async () => {
      return await fn();
    });
  }

  /**
   * Check if Redlock is available
   */
  isAvailable(): boolean {
    return !!this.redlock;
  }

  /**
   * Get the current prefix
   */
  getPrefix(): string {
    return this.keyPrefix;
  }

  /**
   * Cleanup resources when the service is destroyed
   */
  async onModuleDestroy() {
    if (this.redisClient) {
      await this.redisClient.quit();
    }
  }
}
