import { OrchestrationModule } from '@modules/shared/orchestration/orchestration.module';
import { Module } from '@nestjs/common';

import { PrismaModule } from '../prisma/prisma.module';
import { HealthcheckController } from './healthcheck.controller';

@Module({
  imports: [PrismaModule, OrchestrationModule],
  controllers: [HealthcheckController],
  providers: [],
})
export class HealthcheckModule {}
