import { OrchestrationService } from '@modules/shared/orchestration/orchestration.service';
import { Controller, Get } from '@nestjs/common';

import { PrismaService } from '../prisma/prisma.service';

@Controller('healthcheck')
export class HealthcheckController {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly orchestrationService: OrchestrationService,
  ) {}

  @Get()
  async check() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const hasReadReplica = this.prismaService.$replica ? true : false;
    const result = await this.prismaService.readReplica().$queryRaw`SELECT 1`;

    const isPrimaryInstance =
      await this.orchestrationService.isPrimaryInstance();

    const primaryInstanceId =
      await this.orchestrationService.getPrimaryInstanceId();

    return {
      status: 200,
      database: result[0] ? 'OK' : 'FAIL',
      message: 'OK',
      instanceId: this.orchestrationService.getInstanceId(),
      primaryInstanceId,
      isPrimaryInstance,
      hasReadReplica,
    };
  }
}
