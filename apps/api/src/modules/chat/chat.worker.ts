import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { OrchestrationService } from '../shared/orchestration/orchestration.service';

@Injectable()
export class ChatWorker {
  private readonly logger = new Logger(ChatWorker.name);
  private readonly disabled: boolean;

  constructor(
    private readonly prisma: PrismaService,
    private readonly orchestrationService: OrchestrationService,
  ) {
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);
  }

  //@Cron(CronExpression.EVERY_5_MINUTES)
  @Cron(CronExpression.EVERY_5_SECONDS)
  async fixMissingConversationsCron() {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'fixMissingConversationsCron-cron',
          ttl: 1000 * 60 * 4, // 5 minutes
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.fixMissingConversations();
        },
      );
    } catch (error) {
      this.logger.error('Error in fixMissingConversationsCron', error);
    }
  }

  async fixMissingConversations() {
    const patients = await this.prisma.patient.findMany({
      where: {
        doctorId: { not: null },
        status: { notIn: ['cancelled', 'deleted'] },
        OR: [
          //no conversations at all
          {
            conversations: { none: {} },
          },
          //incompeted patientDoctor conversations
          {
            conversations: {
              some: {
                type: 'patientDoctor',
                watcher: {
                  none: {
                    id: { not: undefined },
                    AND: { id: { not: undefined } },
                  },
                },
              },
            },
          },
          //incompeted doctorAdmin conversations
          {
            conversations: {
              none: { type: 'doctorAdmin' },
            },
          },
          // doctorAdmin conversations with no watcher
          //FIXME: what about conversations with no watcher that belongs to the doctor?
          {
            conversations: {
              some: {
                type: 'doctorAdmin',
                watcher: { none: {} },
              },
            },
          },
        ],
      },
      include: {
        user: true,
        doctor: true,
        conversations: {
          where: { type: 'doctorAdmin' },
          include: {
            watcher: true,
          },
        },
      },
    });

    this.logger.debug(
      `Found ${patients.length} patients with missing conversations`,
    );

    for (const patient of patients) {
      this.logger.debug(
        `Fixing patient ${patient.user?.email ?? patient.userId} status ${patient.status}`,
      );

      await this.prisma.$transaction(async (tx) => {
        // patientDoctor conversation
        const conversation = await tx.conversation.findFirst({
          where: {
            patientId: patient.id,
            type: 'patientDoctor',
          },
        });

        let conversationId = null;
        if (!conversation) {
          const c = await tx.conversation.create({
            data: {
              userId: patient.userId,
              patientId: patient.id,
              type: 'patientDoctor',
            },
          });
          conversationId = c.id;
        } else {
          conversationId = conversation.id;
        }

        const watchers = await tx.conversationWatcher.findMany({
          where: { conversationId },
        });

        if (watchers.length < 2) {
          await tx.conversationWatcher.upsert({
            where: {
              conversationId_userId: {
                conversationId,
                userId: patient.userId,
              },
            },
            create: {
              userId: patient.userId,
              conversationId,
            },
            update: {},
          });

          if (patient.doctor && patient.doctor.userId) {
            await tx.conversationWatcher.upsert({
              where: {
                conversationId_userId: {
                  conversationId,
                  userId: patient.doctor.userId,
                },
              },
              create: {
                userId: patient.doctor.userId,
                conversationId,
              },
              update: {},
            });
          }
        }

        // doctorAdmin conversation
        let doctorAdminConversation: any = patient.conversations.find(
          (c) => c.type === 'doctorAdmin',
        );
        if (!doctorAdminConversation) {
          doctorAdminConversation = await tx.conversation.create({
            data: {
              userId: patient.id,
              patientId: patient.id,
              type: 'doctorAdmin',
            },
          });
        }

        // watcher para el doctor en doctorAdmin
        const doctorAdminWatchers = await tx.conversationWatcher.findMany({
          where: { conversationId: doctorAdminConversation.id },
        });
        const doctorUserId = patient.doctorId;
        if (
          doctorUserId &&
          !doctorAdminWatchers.some((w) => w.userId === doctorUserId)
        ) {
          await tx.conversationWatcher.upsert({
            where: {
              conversationId_userId: {
                conversationId: doctorAdminConversation.id,
                userId: doctorUserId,
              },
            },
            create: {
              userId: doctorUserId,
              conversationId: doctorAdminConversation.id,
            },
            update: {},
          });
        }
      });
    }
  }
}
