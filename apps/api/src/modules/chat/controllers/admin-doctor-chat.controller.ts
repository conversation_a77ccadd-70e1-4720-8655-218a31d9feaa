import { CapabilityGuard } from '@/modules/auth/guards/capability.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { SendMessageInput } from '@modules/chat/types/chat.types';
import { CreateAdminDoctorConversationUseCase } from '@modules/chat/use-cases/create-admin-doctor-conversation.use-case';
import {
  AdminConversationFilter,
  GetAdminConversationsUseCase,
} from '@modules/chat/use-cases/get-admin-conversations.use-case';
import { ManageAdminConversationAssignmentUseCase } from '@modules/chat/use-cases/manage-admin-conversation-assignment.use-case';
import { SendMessageUseCase } from '@modules/chat/use-cases/send-message.use-case';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';

@Controller('admin-doctor-chat')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles([roles.Admin, roles.Doctor])
export class AdminDoctorChatController {
  constructor(
    private readonly createAdminDoctorConversationUseCase: CreateAdminDoctorConversationUseCase,
    private readonly getAdminConversationsUseCase: GetAdminConversationsUseCase,
    private readonly manageAdminConversationAssignmentUseCase: ManageAdminConversationAssignmentUseCase,
    private readonly sendMessageUseCase: SendMessageUseCase, // <--- inject SendMessageUseCase
  ) {}

  @Post('conversations')
  @Roles([roles.Admin, roles.Doctor])
  async createConversation(
    @Body() body: { patientId: string; doctorUserId: string },
    @Req() req: Request,
  ) {
    const { patientId, doctorUserId } = body;
    const adminUserId = req.user['userId'];

    if (!patientId || !doctorUserId) {
      throw new BadRequestException('patientId and doctorUserId are required');
    }

    const conversationId =
      await this.createAdminDoctorConversationUseCase.execute({
        patientId,
        adminUserId,
        doctorUserId,
      });

    return { conversationId };
  }

  @Get('conversations')
  @Roles([roles.Admin])
  async getAdminConversations(
    @Query('filter') filter: AdminConversationFilter = 'all',
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Req() req: Request,
  ) {
    const adminUserId = req.user['userId'];
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || pageNum < 1) {
      throw new BadRequestException('Invalid page number');
    }

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      throw new BadRequestException(
        'Invalid limit (must be between 1 and 100)',
      );
    }

    const validFilters: AdminConversationFilter[] = [
      'myInbox',
      'all',
      'unassigned',
      'closed',
    ];
    if (!validFilters.includes(filter)) {
      throw new BadRequestException(
        `Invalid filter. Must be one of: ${validFilters.join(', ')}`,
      );
    }

    return this.getAdminConversationsUseCase.execute({
      adminUserId,
      filter,
      page: pageNum,
      limit: limitNum,
    });
  }

  @Put('conversations/:conversationId/assign')
  @Roles([roles.Admin])
  async assignConversation(
    @Param('conversationId') conversationId: string,
    @Body() body: { adminUserId?: string },
    @Req() req: Request,
  ) {
    const adminUserId = body.adminUserId || req.user['userId'];

    return this.manageAdminConversationAssignmentUseCase.assignConversation({
      conversationId,
      adminUserId,
    });
  }

  @Put('conversations/:conversationId/unassign')
  @Roles([roles.Admin])
  async unassignConversation(@Param('conversationId') conversationId: string) {
    return this.manageAdminConversationAssignmentUseCase.unassignConversation({
      conversationId,
    });
  }

  @Put('conversations/:conversationId/close')
  @Roles([roles.Admin])
  async closeConversation(
    @Param('conversationId') conversationId: string,
    @Req() req: Request,
  ) {
    const adminUserId = req.user['userId'];

    return this.manageAdminConversationAssignmentUseCase.closeConversation({
      conversationId,
      adminUserId,
    });
  }

  @Put('conversations/:conversationId/reopen')
  @Roles([roles.Admin])
  async reopenConversation(@Param('conversationId') conversationId: string) {
    return this.manageAdminConversationAssignmentUseCase.reopenConversation({
      conversationId,
    });
  }

  /**
   * Send a message in a doctorAdmin conversation (admin or doctor only)
   */
  @Post('conversations/messages')
  @Roles([roles.Admin, roles.Doctor])
  async sendMessage(
    @Body() body: Omit<SendMessageInput, 'userId'>,
    @Req() req: Request,
  ) {
    const userId = req.user['userId'];
    const { conversationId, role, content, contentType, type, needsReply } =
      body;
    if (!conversationId || !role) {
      throw new BadRequestException('conversationId and role are required');
    }
    if (!content || !contentType || !type) {
      throw new BadRequestException(
        'content, contentType, and type are required',
      );
    }
    if (role !== 'Admin' && role !== 'Doctor') {
      throw new BadRequestException('Role must be Admin or Doctor');
    }
    const input: SendMessageInput = {
      userId,
      conversationId,
      content,
      contentType,
      type,
      role,
      needsReply,
    };
    const result = await this.sendMessageUseCase.execute(input);
    return { message: 'Message sent', result };
  }
}
