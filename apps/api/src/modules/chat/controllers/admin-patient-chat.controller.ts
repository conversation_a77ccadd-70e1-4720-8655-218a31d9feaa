import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { SendMessageInput } from '@modules/chat/types/chat.types';
import { CreateAdminPatientConversationUseCase } from '@modules/chat/use-cases/create-admin-patient-conversation.use-case';
import {
  AdminConversationFilter,
  GetAdminConversationsUseCase,
} from '@modules/chat/use-cases/get-admin-conversations.use-case';
import { GetConversationMessagesUseCase } from '@modules/chat/use-cases/get-conversation-messages.use-case';
import { ImageUploadGetPreSignedUrlUseCase } from '@modules/chat/use-cases/image-upload-get-pre-signed-url.use-case';
import { ManageAdminConversationAssignmentUseCase } from '@modules/chat/use-cases/manage-admin-conversation-assignment.use-case';
import { MarkAsReadUseCase } from '@modules/chat/use-cases/mark-as-read.use-case';
import { SendMessageUseCase } from '@modules/chat/use-cases/send-message.use-case';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Request } from 'express';

@Controller('admin-patient-chat')
@UseGuards(RolesGuard)
@Roles([roles.Admin, roles.Patient])
export class AdminPatientChatController {
  constructor(
    private readonly createAdminPatientConversationUseCase: CreateAdminPatientConversationUseCase,
    private readonly getAdminConversationsUseCase: GetAdminConversationsUseCase,
    private readonly getConversationMessagesUseCase: GetConversationMessagesUseCase,
    private readonly manageAdminConversationAssignmentUseCase: ManageAdminConversationAssignmentUseCase,
    private readonly markAsReadUseCase: MarkAsReadUseCase,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly imageUploadGetPreSignedUrlUseCase: ImageUploadGetPreSignedUrlUseCase,
  ) {}

  /**
   * Create a new admin-patient conversation
   */
  @Post('conversations')
  @Roles([roles.Admin])
  async createConversation(
    @Body() body: { patientId: string },
    @Req() req: Request,
  ) {
    const { patientId } = body;
    const adminUserId = req.user['userId'];

    if (!patientId) {
      throw new BadRequestException('patientId is required');
    }

    const conversationId =
      await this.createAdminPatientConversationUseCase.execute({
        patientId,
        adminUserId,
      });

    return { conversationId };
  }

  /**
   * Get admin-patient conversations with filtering
   */
  @Get('conversations')
  @Roles([roles.Admin])
  async getAdminPatientConversations(
    @Query('filter') filter: AdminConversationFilter = 'all',
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Req() req: Request,
  ) {
    const adminUserId = req.user['userId'];
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || pageNum < 1) {
      throw new BadRequestException('Invalid page number');
    }

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      throw new BadRequestException(
        'Invalid limit (must be between 1 and 100)',
      );
    }

    const validFilters: AdminConversationFilter[] = [
      'myInbox',
      'all',
      'unassigned',
      'closed',
    ];
    if (!validFilters.includes(filter)) {
      throw new BadRequestException(
        `Invalid filter. Must be one of: ${validFilters.join(', ')}`,
      );
    }

    return this.getAdminConversationsUseCase.execute({
      adminUserId,
      filter,
      page: pageNum,
      limit: limitNum,
      conversationType: 'patientAdmin', // Filter for admin-patient conversations
    });
  }

  /**
   * Get messages for a specific admin-patient conversation
   */
  @Get('conversations/:conversationId/messages')
  @Roles([roles.Admin, roles.Patient])
  async getConversationMessages(
    @Param('conversationId') conversationId: string,
    @Req() req: Request,
  ) {
    const userId = req.user['userId'];
    return this.getConversationMessagesUseCase.execute(conversationId, userId);
  }

  /**
   * Send a message in an admin-patient conversation
   */
  @Post('conversations/messages')
  @Roles([roles.Admin, roles.Patient])
  async sendMessage(
    @Body() body: Omit<SendMessageInput, 'userId'>,
    @Req() req: Request,
  ) {
    const userId = req.user['userId'];
    const { conversationId, role, content, contentType, type, needsReply } =
      body;
    
    if (!conversationId || !role) {
      throw new BadRequestException('conversationId and role are required');
    }
    if (!content || !contentType || !type) {
      throw new BadRequestException(
        'content, contentType, and type are required',
      );
    }
    if (role !== 'Admin' && role !== 'Patient') {
      throw new BadRequestException('Role must be Admin or Patient');
    }

    return this.sendMessageUseCase.execute({
      userId,
      conversationId,
      role,
      content,
      contentType,
      type,
      needsReply,
    });
  }

  /**
   * Mark conversation as read
   */
  @Post('conversations/:conversationId/read')
  @Roles([roles.Admin, roles.Patient])
  async markAsRead(
    @Param('conversationId') conversationId: string,
    @Req() req: Request,
  ) {
    const userId = req.user['userId'];
    return this.markAsReadUseCase.execute(conversationId, userId);
  }

  /**
   * Get pre-signed URL for image upload
   */
  @Get('conversations/:conversationId/get-upload-url/:filename')
  @Roles([roles.Admin, roles.Patient])
  async getUploadUrl(
    @Param('conversationId') conversationId: string,
    @Param('filename') filename: string,
  ) {
    return this.imageUploadGetPreSignedUrlUseCase.execute(
      conversationId,
      filename,
    );
  }

  /**
   * Assign conversation to admin
   */
  @Put('conversations/:conversationId/assign')
  @Roles([roles.Admin])
  async assignConversation(
    @Param('conversationId') conversationId: string,
    @Body() body: { adminUserId?: string },
    @Req() req: Request,
  ) {
    const adminUserId = body.adminUserId || req.user['userId'];

    return this.manageAdminConversationAssignmentUseCase.assignConversation({
      conversationId,
      adminUserId,
    });
  }

  /**
   * Unassign conversation from admin
   */
  @Put('conversations/:conversationId/unassign')
  @Roles([roles.Admin])
  async unassignConversation(@Param('conversationId') conversationId: string) {
    return this.manageAdminConversationAssignmentUseCase.unassignConversation({
      conversationId,
    });
  }

  /**
   * Close conversation
   */
  @Put('conversations/:conversationId/close')
  @Roles([roles.Admin])
  async closeConversation(
    @Param('conversationId') conversationId: string,
    @Req() req: Request,
  ) {
    const adminUserId = req.user['userId'];

    return this.manageAdminConversationAssignmentUseCase.closeConversation({
      conversationId,
      adminUserId,
    });
  }
}
