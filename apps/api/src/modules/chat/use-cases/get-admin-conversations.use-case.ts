import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { ConversationType, Prisma } from '@prisma/client';

export type AdminConversationFilter =
  | 'myInbox' // assigned to current admin
  | 'all' // all open conversations
  | 'unassigned' // unassigned open conversations
  | 'closed'; // conversations closed in last 7 days

@Injectable()
export class GetAdminConversationsUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute({
    adminUserId,
    filter,
    page = 1,
    limit = 20,
    conversationType,
  }: {
    adminUserId: string;
    filter: AdminConversationFilter;
    page?: number;
    limit?: number;
    conversationType?: ConversationType;
  }) {
    // Verify the user is an admin
    const admin = await this.prismaService.user.findFirst({
      where: {
        id: adminUserId,
        type: 'admin',
      },
      include: {
        admin: true,
      },
    });

    if (!admin || !admin.admin) {
      throw new Error('Admin user not found');
    }

    // Build where clause based on filter
    const whereClause: Prisma.ConversationWhereInput = {
      type: conversationType || 'doctorAdmin', // Default to doctorAdmin for backward compatibility
    };

    switch (filter) {
      case 'myInbox':
        whereClause.assignedAdminId = adminUserId;
        whereClause.status = 'open'; // Only open conversations assigned to admin
        break;

      case 'all':
        whereClause.status = 'open'; // Only open conversations
        break;

      case 'unassigned':
        whereClause.assignedAdminId = null;
        whereClause.status = 'open'; // Only open unassigned conversations
        break;

      case 'closed':
        whereClause.status = 'closed'; // Closed conversations
        whereClause.closedAt = {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        };
        break;
    }

    const skip = (page - 1) * limit;

    // Get conversations with related data
    const [conversations, totalCount] = await Promise.all([
      this.prismaService.conversation.findMany({
        where: whereClause,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      id: true,
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
          assignedAdmin: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  type: true,
                },
              },
            },
          },
          watcher: {
            where: { userId: adminUserId },
            select: {
              unreadMessages: true,
              updatedAt: true,
            },
          },
        },
        orderBy: [{ updatedAt: 'desc' }, { createdAt: 'desc' }],
        skip,
        take: limit,
      }),
      this.prismaService.conversation.count({
        where: whereClause,
      }),
    ]);

    return {
      conversation: conversations.map((conversation) => ({
        id: conversation.id,
        patientId: conversation.patientId,
        status: conversation.status,
        assignedAdminId: conversation.assignedAdminId,
        lastMessageText: conversation.lastMessageText,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
        closedAt: conversation.closedAt,
        patient: {
          id: conversation.patient.id,
          user: conversation.patient.user,
          doctor: conversation.patient.doctor,
        },
        assignedAdmin: conversation.assignedAdmin,
        lastMessage: conversation.messages[0] || null,
        unreadMessages: conversation.watcher[0]?.unreadMessages || 0,
      })),
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page * limit < totalCount,
        hasPreviousPage: page > 1,
      },
    };
  }
}
