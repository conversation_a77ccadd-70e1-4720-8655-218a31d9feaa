import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { PatientUpdatedQueueEvent } from '@/modules/shared/events/patient-topic.definition';
import { Injectable } from '@nestjs/common';
import { ConversationType } from '@prisma/client';

@Injectable()
export class CreateConversationUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  @SnsConsume({
    topic: 'patient-updated',
    consumerGroup: 'create-chat-service',
    filter: ['doctor_assigned'],
  })
  async handleSnsMessage({ payload }: PatientUpdatedQueueEvent) {
    const { patient } = payload;
    return this.execute({
      doctorUserId: patient.doctor.userId,
      patientUserId: patient.id,
    });
  }

  async execute({
    doctorUserId,
    patientUserId,
  }: {
    doctorUserId: string;
    patientUserId: string;
  }) {
    const conversationsTypes: ConversationType[] = [
      'patientDoctor',
      'doctorAdmin',
    ];

    try {
      const conversationIds = await Promise.all(
        conversationsTypes.map((conversationType) =>
          this.ensureConversationType({
            conversationType,
            doctorUserId,
            patientUserId,
          }).catch((err) => {
            // Log and return null if one fails, but continue with others
            console.error(
              `Error ensuring conversation of type ${conversationType}:`,
              err,
            );
            return null;
          }),
        ),
      );
      // Filtra los nulos y retorna solo los ids válidos
      return conversationIds.filter(Boolean);
    } catch (error) {
      // Log global error y retorna array vacío
      console.error('Error in execute (CreateConversationUseCase):', error);
      return [];
    }
  }

  private async ensureConversationType({
    conversationType,
    doctorUserId,
    patientUserId,
  }: {
    conversationType: ConversationType;
    doctorUserId: string;
    patientUserId: string;
  }) {
    const existingAdminDoctorConversation =
      await this.prismaService.conversation.findFirst({
        where: { type: conversationType, userId: patientUserId },
        include: { watcher: true },
      });

    const exists =
      existingAdminDoctorConversation &&
      existingAdminDoctorConversation.watcher.some(
        (watcher) => watcher.userId === doctorUserId,
      ) &&
      existingAdminDoctorConversation.watcher.some(
        (watcher) => watcher.userId === patientUserId,
      );

    if (exists) return existingAdminDoctorConversation.id;

    // create conversation
    return this.prismaService.$transaction(async (prisma) => {
      const patient = await prisma.patient.findUnique({
        where: { userId: patientUserId },
      });

      const conversation = await prisma.conversation.upsert({
        where: {
          patientId_type: {
            patientId: patient.id,
            type: 'patientDoctor',
          },
        },
        create: {
          userId: patientUserId,
          patientId: patient.id,
          updatedAt: null,
        },
        update: {},
      });

      // create watchers for both patient and doctor
      await prisma.conversationWatcher.upsert({
        where: {
          conversationId_userId: {
            conversationId: conversation.id,
            userId: patientUserId,
          },
        },
        create: { userId: patientUserId, conversationId: conversation.id },
        update: {},
      });

      await prisma.conversationWatcher.upsert({
        where: {
          conversationId_userId: {
            conversationId: conversation.id,
            userId: doctorUserId,
          },
        },
        create: { userId: doctorUserId, conversationId: conversation.id },
        update: {},
      });

      return conversation.id;
    });
  }
}
