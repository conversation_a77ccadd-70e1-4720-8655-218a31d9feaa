import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreateAdminDoctorConversationUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute({
    patientId,
    adminUserId,
    doctorUserId,
  }: {
    patientId: string;
    adminUserId?: string;
    doctorUserId?: string;
  }) {
    // Check if admin-doctor conversation already exists for this patient
    const existingConversation =
      await this.prismaService.conversation.findFirst({
        where: {
          patientId,
          type: 'doctorAdmin',
        },
        include: { watcher: true },
      });

    if (existingConversation) {
      // Check if both admin and doctor are already watchers (if provided)
      const hasAdminWatcher = adminUserId
        ? existingConversation.watcher.some(
            (watcher) => watcher.userId === adminUserId,
          )
        : false;
      const hasDoctorWatcher = doctorUserId
        ? existingConversation.watcher.some(
            (watcher) => watcher.userId === doctorUserId,
          )
        : false;

      if (
        (adminUserId ? hasAdminWatcher : true) &&
        (doctorUserId ? hasDoctorWatcher : true)
      ) {
        return existingConversation.id;
      }

      // Add missing watchers if IDs are provided
      if (adminUserId && !hasAdminWatcher) {
        await this.prismaService.conversationWatcher.create({
          data: {
            userId: adminUserId,
            conversationId: existingConversation.id,
          },
        });
      }

      if (doctorUserId && !hasDoctorWatcher) {
        await this.prismaService.conversationWatcher.create({
          data: {
            userId: doctorUserId,
            conversationId: existingConversation.id,
          },
        });
      }

      return existingConversation.id;
    }

    // Get patient information
    const patient = await this.prismaService.patient.findUnique({
      where: { id: patientId },
      include: { user: true },
    });

    if (!patient) {
      throw new Error(`Patient not found: ${patientId}`);
    }

    // Create new admin-doctor conversation
    return this.prismaService.$transaction(async (prisma) => {
      const conversation = await prisma.conversation.create({
        data: {
          userId: patient.userId, // Still point to patient user for consistency
          patientId: patient.id,
          type: 'doctorAdmin',
          status: 'open', // Use 'open' status for admin-doctor conversations
          assignedAdminId: null,
          updatedAt: null,
        },
      });

      // Create watchers for admin and doctor if IDs are provided
      await Promise.all([
        adminUserId
          ? prisma.conversationWatcher.create({
              data: {
                userId: adminUserId,
                conversationId: conversation.id,
              },
            })
          : Promise.resolve(),
        doctorUserId
          ? prisma.conversationWatcher.create({
              data: {
                userId: doctorUserId,
                conversationId: conversation.id,
              },
            })
          : Promise.resolve(),
      ]);

      return conversation.id;
    });
  }
}
