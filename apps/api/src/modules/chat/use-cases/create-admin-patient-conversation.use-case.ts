import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreateAdminPatientConversationUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute({
    patientId,
    adminUserId,
  }: {
    patientId: string;
    adminUserId?: string;
  }) {
    // Check if admin-patient conversation already exists for this patient
    const existingConversation =
      await this.prismaService.conversation.findFirst({
        where: {
          patientId,
          type: 'patientAdmin',
        },
        include: { watcher: true },
      });

    if (existingConversation) {
      // If admin is provided and not already a watcher, add them
      if (adminUserId) {
        const isAdminWatcher = existingConversation.watcher.some(
          (w) => w.userId === adminUserId,
        );

        if (!isAdminWatcher) {
          await this.prismaService.conversationWatcher.create({
            data: {
              userId: adminUserId,
              conversationId: existingConversation.id,
            },
          });
        }
      }

      return existingConversation.id;
    }

    // Verify patient exists
    const patient = await this.prismaService.patient.findFirst({
      where: { id: patientId },
      include: { user: true },
    });

    if (!patient) {
      throw new Error('Patient not found');
    }

    // Verify admin exists if provided
    if (adminUserId) {
      const admin = await this.prismaService.user.findFirst({
        where: {
          id: adminUserId,
          type: 'admin',
        },
        include: { admin: true },
      });

      if (!admin || !admin.admin) {
        throw new Error('Admin user not found');
      }
    }

    // Create new admin-patient conversation
    return this.prismaService.$transaction(async (prisma) => {
      const conversation = await prisma.conversation.create({
        data: {
          userId: patient.userId, // Point to patient user for consistency
          patientId: patient.id,
          type: 'patientAdmin',
          status: 'open', // Use 'open' status for admin-patient conversations
          assignedAdminId: null,
          updatedAt: null,
        },
      });

      // Create watchers for patient and admin if provided
      const watcherPromises = [
        // Always add patient as watcher
        prisma.conversationWatcher.create({
          data: {
            userId: patient.userId,
            conversationId: conversation.id,
          },
        }),
      ];

      // Add admin as watcher if provided
      if (adminUserId) {
        watcherPromises.push(
          prisma.conversationWatcher.create({
            data: {
              userId: adminUserId,
              conversationId: conversation.id,
            },
          }),
        );
      }

      await Promise.all(watcherPromises);

      return conversation.id;
    });
  }
}
