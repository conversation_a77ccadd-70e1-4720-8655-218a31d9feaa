import { PrismaService } from '@/modules/prisma/prisma.service';
import { SqsService } from '@modules/shared/aws/sqs/sqs.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { bulkTransferStatus } from '@prisma/client';

import { OrchestrationService } from '../shared/orchestration/orchestration.service';
import { TransferPharmaciesDto } from './dto/transfer-pharmacies.dto';

interface PharmacyTransferQueueItem {
  targetPharmacyId: string;
  patientId: string;
  reason: string;
  bulkTransferId?: string;
}

@Injectable()
export class PharmacyTransferService {
  private readonly logger = new Logger(PharmacyTransferService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly sqsService: SqsService,
    private readonly orchestrationService: OrchestrationService,
  ) {
    this.logger.log('PharmacyTransferService initialized with cron jobs');
  }

  /**
   * Transfer patients from one pharmacy to another based on states
   * @param dto Transfer configuration with source pharmacy, target pharmacy, and states
   * @returns Object with transfer results and bulk transfer ID
   */
  async transferPharmacies(dto: TransferPharmaciesDto): Promise<{
    queuedTransfers: number;
    statesCovered: string[];
    errors: { message: string; details?: any }[];
    bulkTransferId?: string;
  }> {
    const errors: { message: string; details?: any }[] = [];

    // Validate source pharmacy exists
    const sourcePharmacy = await this.prisma.pharmacy.findUnique({
      where: { id: dto.sourcePharmacyId },
      include: {
        PharmacyOnState: {
          include: { state: true },
        },
      },
    });

    if (!sourcePharmacy) {
      errors.push({
        message: `Source pharmacy with ID ${dto.sourcePharmacyId} not found`,
      });
      return { queuedTransfers: 0, statesCovered: [], errors };
    }

    // Validate target pharmacy exists
    const targetPharmacy = await this.prisma.pharmacy.findUnique({
      where: { id: dto.targetPharmacyId, enabled: true },
      include: {
        PharmacyOnState: {
          include: { state: true },
        },
      },
    });

    if (!targetPharmacy) {
      errors.push({
        message: `Target pharmacy with ID ${dto.targetPharmacyId} not found or not enabled`,
      });
      return { queuedTransfers: 0, statesCovered: [], errors };
    }

    // Validate states exist and are valid for both pharmacies
    const states = await this.prisma.state.findMany({
      where: { id: { in: dto.stateIds } },
      select: { id: true, code: true, name: true },
    });

    if (states.length !== dto.stateIds.length) {
      const foundIds = states.map((s) => s.id);
      const missingIds = dto.stateIds.filter((id) => !foundIds.includes(id));
      errors.push({
        message: 'Some state IDs are invalid',
        details: { missingIds },
      });
    }

    // Create sets of states for each pharmacy
    const targetPharmacyStates = new Set(
      targetPharmacy.PharmacyOnState.map((pos) => pos.stateId),
    );

    const invalidTargetStates = dto.stateIds.filter(
      (stateId) => !targetPharmacyStates.has(stateId),
    );

    if (invalidTargetStates.length > 0) {
      errors.push({
        message: 'Some states are not valid for the target pharmacy',
        details: { invalidTargetStates },
      });
    }

    // If there are validation errors, return them
    if (errors.length > 0) {
      return { queuedTransfers: 0, statesCovered: [], errors };
    }

    // Find all patients in the specified states assigned to the source pharmacy
    // to get the count of patients that will be affected
    const patientsCount = await this.prisma.patient.count({
      where: {
        pharmacyId: dto.sourcePharmacyId,
        stateId: { in: dto.stateIds },
      },
    });

    this.logger.log(
      `Found ${patientsCount} patients that will be affected by the pharmacy transfer`,
    );

    // Set default reason if not provided
    const reason =
      dto.reason ||
      `Pharmacy transfer from ${sourcePharmacy.name} to ${targetPharmacy.name}`;

    // Create a BulkTransfer record to track this operation
    const bulkTransfer = await this.prisma.bulkTransfer.create({
      data: {
        type: 'pharmacy',
        pharmacyId: dto.sourcePharmacyId,
        reason: reason,
        rules: {
          sourcePharmacyId: dto.sourcePharmacyId,
          targetPharmacyId: dto.targetPharmacyId,
          stateIds: dto.stateIds,
          forms: dto.forms || [],
        },
        status: bulkTransferStatus.pending,
        queuedJobs: patientsCount,
      },
    });

    this.logger.log(
      `Created bulk transfer record ${bulkTransfer.id} for pharmacy transfer`,
    );

    return {
      queuedTransfers: patientsCount,
      statesCovered: dto.stateIds,
      errors,
      bulkTransferId: bulkTransfer.id,
    };
  }

  /**
   * Process pharmacy transfers for a bulk transfer
   */
  private async processPharmacyTransfer(
    bulkTransferId: string,
    rules: any,
    reason: string,
  ): Promise<{
    queuedTransfers: number;
    errors: { message: string; details?: any }[];
  }> {
    const errors: { message: string; details?: any }[] = [];

    // Extract data from rules
    const sourcePharmacyId = rules.sourcePharmacyId;
    const targetPharmacyId = rules.targetPharmacyId;
    const stateIds = rules.stateIds;
    const forms = rules.forms || [];

    // Find all patients in the specified states assigned to the source pharmacy
    // If forms are specified, only include patients with treatments using those forms
    const patients = await this.prisma.patient.findMany({
      where: {
        pharmacyId: sourcePharmacyId,
        stateId: { in: stateIds },
      },
      select: {
        id: true,
        stateId: true,
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        treatment:
          forms.length > 0
            ? {
                where: {
                  initialProductPrice: {
                    product: {
                      form: { in: forms },
                    },
                  },
                },
                select: {
                  id: true,
                },
              }
            : undefined,
      },
    });

    // If forms are specified, filter patients to only include those with matching treatments
    const filteredPatients =
      forms.length > 0
        ? patients.filter(
            (patient) => patient.treatment && patient.treatment.length > 0,
          )
        : patients;

    if (filteredPatients.length === 0) {
      this.logger.log(
        `No patients found to transfer for bulk transfer ${bulkTransferId}`,
      );
      return {
        queuedTransfers: 0,
        errors: [
          {
            message: 'No patients found to transfer',
          },
        ],
      };
    }

    this.logger.log(
      `Found ${filteredPatients.length} patients to transfer for bulk transfer ${bulkTransferId}${forms.length > 0 ? ` with forms: ${forms.join(', ')}` : ''}`,
    );

    // Prepare transfer queue
    const transferQueue: PharmacyTransferQueueItem[] = filteredPatients.map(
      (patient) => ({
        targetPharmacyId,
        patientId: patient.id,
        reason,
        bulkTransferId,
      }),
    );

    // Queue all transfers using SQS
    const queueName = 'pharmacy-transfers';
    const queueUrl = await this.sqsService.ensureQueueExists({
      queueName,
      deadLetterQueueName: 'pharmacy-transfers-dlq',
    });

    this.logger.log(`Using SQS queue: ${queueUrl} for pharmacy transfers`);

    // Process transfers in batches of 10 (AWS SQS batch limit)
    const BATCH_SIZE = 10;
    let queuedCount = 0;

    for (let i = 0; i < transferQueue.length; i += BATCH_SIZE) {
      const batch = transferQueue.slice(i, i + BATCH_SIZE);

      // Prepare batch messages for SQS
      const batchMessages = batch.map((transfer, index) => {
        // Create a valid batch entry ID (alphanumeric, hyphens, underscores, max 80 chars)
        // Use index to ensure uniqueness within the batch
        const batchId =
          `pharmacy-transfer-${transfer.patientId.substring(0, 8)}-${index}-${Date.now()}`.substring(
            0,
            80,
          );

        return {
          id: batchId,
          message: {
            metadata: {
              eventId: `pharmacy-transfer-${transfer.patientId}-${transfer.targetPharmacyId}-${Date.now()}`,
              timestamp: new Date().toISOString(),
            },
            payload: {
              targetPharmacyId: transfer.targetPharmacyId,
              patientId: transfer.patientId,
              reason: transfer.reason,
              bulkTransferId: transfer.bulkTransferId,
            },
          },
        };
      });

      // Send batch of messages
      const result = await this.sqsService.sendMessageBatch({
        queueName,
        messages: batchMessages,
      });

      // Count successful messages
      queuedCount += result.successful.length;

      // Log any failures
      if (result.failed.length > 0) {
        this.logger.error(
          `Failed to queue ${result.failed.length} pharmacy transfers for bulk transfer ${bulkTransferId}`,
        );
      }

      // Add a small delay between batches
      if (i + BATCH_SIZE < transferQueue.length) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    // Update the bulk transfer record with the number of queued jobs
    if (bulkTransferId && queuedCount > 0) {
      await this.prisma.bulkTransfer.update({
        where: {
          id: bulkTransferId,
          type: 'pharmacy',
        },
        data: { queuedJobs: queuedCount },
      });
    }

    return {
      queuedTransfers: queuedCount,
      errors,
    };
  }

  /**
   * Cron job to process pending bulk transfers and cleanup stalled transfers
   * Runs every minute to check for transfers that need to be processed
   */
  async processPendingBulkTransfers(): Promise<void> {
    this.logger.log(
      'Checking for pending bulk pharmacy transfers and stalled transfers',
    );

    // First, check for stalled transfers that have been in progress for too long (>24 hours)
    const stalledTransfers = await this.prisma.bulkTransfer.findMany({
      where: {
        type: 'pharmacy',
        status: bulkTransferStatus.inProgress,
        createdAt: {
          lt: new Date(Date.now() - 24 * 3600 * 1000), // 24 hours ago
        },
      },
    });

    if (stalledTransfers.length > 0) {
      this.logger.log(
        `Found ${stalledTransfers.length} stalled bulk pharmacy transfers to clean up`,
      );

      for (const transfer of stalledTransfers) {
        try {
          this.logger.warn(
            `Bulk pharmacy transfer ${transfer.id} stalled after ${Math.floor(
              (Date.now() - transfer.createdAt.getTime()) / 60000,
            )} minutes - marking as completed`,
          );

          await this.prisma.bulkTransfer.update({
            where: {
              id: transfer.id,
              type: 'pharmacy',
            },
            data: {
              status: bulkTransferStatus.completed,
              completedAt: new Date(),
            },
          });
        } catch (error) {
          this.logger.error(
            `Error cleaning up stalled pharmacy transfer ${transfer.id}:`,
            error.stack,
          );
        }
      }
    }

    // Check for transfers where completedJobs equals queuedJobs but status is still inProgress
    const completedTransfers = await this.prisma.bulkTransfer.findMany({
      where: {
        type: 'pharmacy',
        status: bulkTransferStatus.inProgress,
        queuedJobs: { gt: 0 }, // Ensure we only check transfers that have jobs
      },
    });

    for (const transfer of completedTransfers) {
      if (transfer.completedJobs === transfer.queuedJobs) {
        this.logger.log(
          `Marking bulk pharmacy transfer ${transfer.id} as completed (${transfer.completedJobs}/${transfer.queuedJobs} jobs completed)`,
        );

        await this.prisma.bulkTransfer.update({
          where: {
            id: transfer.id,
            type: 'pharmacy',
          },
          data: {
            status: bulkTransferStatus.completed,
            completedAt: new Date(),
          },
        });
      }
    }

    // Then, find bulk transfers that are scheduled to be executed now or in the past
    const pendingTransfers = await this.prisma.bulkTransfer.findMany({
      where: {
        type: 'pharmacy',
        status: bulkTransferStatus.pending,
        transferAt: {
          lte: new Date(),
        },
      },
      orderBy: {
        transferAt: 'asc',
      },
      take: 5, // Process a few at a time to avoid overwhelming the system
    });

    if (pendingTransfers.length === 0) return;

    this.logger.log(
      `Processing ${pendingTransfers.length} pending bulk pharmacy transfers`,
    );

    for (const transfer of pendingTransfers) {
      try {
        // Check if another job is already processing this transfer (in case of concurrent execution)
        const currentTransfer = await this.prisma.bulkTransfer.findUnique({
          where: {
            id: transfer.id,
            type: 'pharmacy',
          },
        });

        if (currentTransfer.status !== bulkTransferStatus.pending) {
          this.logger.log(
            `Skipping bulk pharmacy transfer ${transfer.id} as it's already being processed (status: ${currentTransfer.status})`,
          );
          continue;
        }

        // Update status to in progress
        await this.prisma.bulkTransfer.update({
          where: {
            id: transfer.id,
            type: 'pharmacy',
          },
          data: { status: bulkTransferStatus.inProgress },
        });

        // Process the transfer and queue all the individual patient transfers
        const transferResult = await this.processPharmacyTransfer(
          transfer.id,
          transfer.rules,
          transfer.reason || 'Scheduled pharmacy transfer',
        );

        if (transferResult.errors.length > 0) {
          this.logger.warn(
            `Bulk pharmacy transfer ${transfer.id} queued with ${transferResult.errors.length} errors: ${JSON.stringify(transferResult.errors)}`,
          );
        }

        // We keep the status as IN_PROGRESS since the actual transfers
        // will complete asynchronously via SQS
        // The status will be updated to COMPLETED when all patient transfers are done

        this.logger.log(
          `Successfully queued ${transferResult.queuedTransfers} transfers for bulk pharmacy transfer ${transfer.id}`,
        );
      } catch (error) {
        this.logger.error(
          `Error processing bulk pharmacy transfer ${transfer.id}:`,
          error.stack,
        );

        // Update status to failed and set completedAt
        await this.prisma.bulkTransfer.update({
          where: {
            id: transfer.id,
            type: 'pharmacy',
          },
          data: {
            status: bulkTransferStatus.failed,
            reason: `${transfer.reason || 'Scheduled pharmacy transfer'} (Failed: ${error.message})`,
            completedAt: new Date(),
          },
        });
      }
    }
  }

  @Cron('0 * * * * *')
  async processPendingBulkTransfersCron(): Promise<void> {
    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'processPendingBulkTransfersCron-cron',
          ttl: 1000 * 60, // 1 minute
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.processPendingBulkTransfers();
        },
      );
    } catch (error) {
      this.logger.error(
        'Error running processPendingBulkTransfers cron job:',
        error,
      );
    }
  }
}
