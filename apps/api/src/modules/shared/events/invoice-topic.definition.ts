import { DraftInvoice } from '@/modules/stripe/service/stripe.service';
import Stripe from 'stripe';

import { BaseEvent, BaseMessage } from './base.definition';

export type StripeInvoiceUpdatedEvent =
  | BaseEvent<
      'create',
      {
        patientId: string;
        skipOldInvoiceCheck?: boolean;
        draftInvoice: DraftInvoice;
      }
    >
  | BaseEvent<
      | 'finalized'
      | 'paid'
      | 'payment_failed'
      | 'uncollectible'
      | 'voided'
      | 'refunded',
      { patientId: string; invoice: Stripe.Invoice }
    >;

export type StripeInvoiceUpdatedQueueEvent = BaseMessage<
  'stripe-invoice-updated',
  StripeInvoiceUpdatedEvent
>;
