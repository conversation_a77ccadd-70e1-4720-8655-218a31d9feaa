import { SnsService } from '@modules/shared/aws/sns/sns.service';
import { OutboxerService } from '@modules/shared/outboxer/outboxer.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Outbox } from '@prisma/client';

import { OrchestrationService } from '../../orchestration/orchestration.service';

@Injectable()
export class OutboxerSnsWorker {
  private readonly logger = new Logger(OutboxerSnsWorker.name);
  private readonly BATCH_SIZE = 20;
  private readonly lockKey = 'outboxer_sns_worker_lock';

  private isDisabled = true;

  constructor(
    private readonly outboxerService: OutboxerService,
    private readonly orchestrationService: OrchestrationService,
    private readonly snsService: SnsService,
    private readonly config: ConfigService,
  ) {
    // Check IS_CLI first, if true, disable this worker
    this.isDisabled =
      this.config.get('IS_CLI') === 'true' ||
      this.config.get('ENABLE_SNS_CONSUMER') !== 'true';
    if (this.isDisabled) {
      console.warn('SNS is disabled, skipping worker initialization');
      return;
    }
  }

  @Cron(CronExpression.EVERY_5_SECONDS)
  async processOutboxMessages() {
    if (this.isDisabled) {
      return;
    }

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: this.lockKey,
          ttl: 5_000,
          thisInstanceMustBePrimary: true,
        },
        async () => {
          try {
            await this.pollAndProcessMessages();
          } catch (error) {
            this.logger.error('Error in SNS worker processing', error);
            throw error;
          }
        },
      );
    } catch (error) {
      this.logger.error('Error in OutboxerSnsWorker', error);
    }
  }

  private async pollAndProcessMessages(): Promise<void> {
    const pendingMessages = await this.outboxerService.searchRetryable({
      destination: 'sns',
      limit: this.BATCH_SIZE,
    });

    if (pendingMessages.length === 0) {
      // this.logger.debug('No pending messages found for SNS');
      return;
    }

    // Process each message
    const processPromises = pendingMessages.map((message) =>
      this.outboxerService.processMessage(
        (msg) => this.sendToSns(msg),
        message,
      ),
    );

    await Promise.allSettled(processPromises);
    this.logger.log(
      `Completed processing batch of ${pendingMessages.length} messages`,
    );
  }

  private async sendToSns(message: Outbox): Promise<void> {
    const payload = message.payload as Record<string, any>;
    const topicName = message.topic || 'default-topic';

    try {
      const result = await this.snsService.publishMessage({
        topicName,
        message: payload,
        subject: (message.payload as { event?: string })?.event,

        ...(message.deduplicationId
          ? {
              deduplicationId: message.deduplicationId,
              // groupId: message.patientId,
            }
          : {}),
      });

      this.logger.debug(
        `Successfully sent message to topic: ${topicName}, message ID: ${result.messageId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send message to SNS topic: ${topicName}`,
        error,
      );
      // Re-throw to mark as failed in outbox
      throw {
        message: 'Failed to send message to SNS',
        code: 'SNS_PUBLISH_ERROR',
        details: {
          error: error.message || String(error),
          topicName,
        },
      };
    }
  }
}
