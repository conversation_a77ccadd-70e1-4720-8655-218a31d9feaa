import { SqsService } from '@modules/shared/aws/sqs/sqs.service';
import { OutboxerService } from '@modules/shared/outboxer/outboxer.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Outbox } from '@prisma/client';

import { OrchestrationService } from '../../orchestration/orchestration.service';

@Injectable()
export class OutboxerSqsWorker {
  private readonly logger = new Logger(OutboxerSqsWorker.name);
  private readonly BATCH_SIZE = 20; // Process 20 messages at a time
  private readonly lockKey = 'outboxer_sqs_worker_lock';

  private isDisabled = true;

  constructor(
    private readonly outboxerService: OutboxerService,
    private readonly orchestrationService: OrchestrationService,
    private readonly sqsService: SqsService,
    private readonly config: ConfigService,
  ) {
    // Check IS_CLI first, if true, disable this worker
    this.isDisabled =
      this.config.get('IS_CLI') === 'true' ||
      this.config.get('ENABLE_SQS_CONSUMER') !== 'true';
    if (this.isDisabled) {
      console.warn('SQS is disabled, skipping worker initialization');
      return;
    }
  }

  @Cron(CronExpression.EVERY_5_SECONDS)
  async processOutboxMessages() {
    if (this.isDisabled) {
      return;
    }

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: this.lockKey,
          ttl: 5_000,
          thisInstanceMustBePrimary: true,
        },
        async () => {
          try {
            await this.pollAndProcessMessages();
          } catch (error) {
            this.logger.error('Error in SNS worker processing', error);
            throw error;
          }
        },
      );
    } catch (error) {
      this.logger.error('Error in OutboxerSqsWorker', error);
    }
  }

  private async pollAndProcessMessages(): Promise<void> {
    const pendingMessages = await this.outboxerService.searchRetryable({
      destination: 'sqs',
      limit: this.BATCH_SIZE,
    });

    if (pendingMessages.length === 0) {
      // this.logger.debug('No pending messages found for SQS');
      return;
    }

    this.logger.log(`Processing ${pendingMessages.length} SQS outbox messages`);

    // Process each message
    const processPromises = pendingMessages.map((message) =>
      this.outboxerService.processMessage(
        (msg) => this.sendToSqs(msg),
        message,
      ),
    );

    await Promise.allSettled(processPromises);
    this.logger.log(
      `Completed processing batch of ${pendingMessages.length} messages`,
    );
  }

  private async sendToSqs(message: Outbox): Promise<void> {
    const payload = message.payload as Record<string, any>;
    const queueName = message.topic || 'default-queue';

    this.logger.debug(`Sending message to SQS queue: ${queueName}`);

    try {
      const result = await this.sqsService.sendMessage({
        queueName,
        message: payload,
      });

      this.logger.debug(
        `Successfully sent message to SQS with ID: ${result.messageId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send message to SQS queue: ${queueName}`,
        error,
      );
      // Re-throw to mark as failed in outbox
      throw {
        message: 'Failed to send message to SQS',
        code: 'SQS_SEND_ERROR',
        details: {
          error: error.message || String(error),
          queueName,
        },
      };
    }
  }
}
