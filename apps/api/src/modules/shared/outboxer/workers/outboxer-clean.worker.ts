import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { OutboxStatus } from '@prisma/client';
import { subDays } from 'date-fns';

import { OrchestrationService } from '../../orchestration/orchestration.service';

@Injectable()
export class OutboxerCleanWorker {
  private disabled: boolean;

  private readonly logger = new Logger(OutboxerCleanWorker.name);
  private readonly MESSAGE_RETENTION_DAYS = 20;

  constructor(
    private readonly prisma: PrismaService,
    private readonly orchestrationService: OrchestrationService,
  ) {
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);
    if (this.disabled) {
      this.logger.warn('OutboxerCleanWorker is disabled in this environment');
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupOldMessages() {
    if (this.disabled) {
      return;
    }

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'cleanupOldMessages-cron',
          ttl: 1000 * 60 * 10, // 10 minutes
          thisInstanceMustBePrimary: true,
        },
        async () => {
          this.logger.log(`Starting cleanup of old outbox messages`);

          // Calculate the cutoff date (7 days ago)
          const cutoffDate = subDays(new Date(), this.MESSAGE_RETENTION_DAYS);

          // Delete all delivered messages older than cutoff date
          const result = await this.prisma.outbox.deleteMany({
            where: {
              status: OutboxStatus.DONE,
              processedAt: {
                lt: cutoffDate,
              },
            },
          });

          this.logger.log(
            `Successfully cleaned up ${result.count} delivered messages older than ${cutoffDate.toISOString()}`,
          );
        },
      );
    } catch (error) {
      this.logger.error('Error cleaning up outbox messages', error);
    }
  }
}
