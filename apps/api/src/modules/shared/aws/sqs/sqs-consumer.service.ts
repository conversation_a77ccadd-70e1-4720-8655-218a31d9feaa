import {
  DeleteMessageCommand,
  ReceiveMessageCommand,
  SQSClient,
} from '@aws-sdk/client-sqs';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DiscoveryService, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { MetadataScanner } from '@nestjs/core/metadata-scanner';

import { BaseQueueMessage } from '../../events/base.definition';
import { SQS_CONSUMER_OPTIONS, SqsConsumerHandler } from './sqs.decorator';
import { SqsService } from './sqs.service';

export interface SqsMessage {
  body: any;
  messageId: string;
  receiptHandle: string;
  attributes: Record<string, any>;
  messageAttributes: Record<string, any>;
  md5OfBody: string;
  eventSource: string;
  eventSourceARN: string;
  awsRegion: string;
}

@Injectable()
export class SqsConsumerService implements OnModuleInit {
  private readonly logger = new Logger(SqsConsumerService.name);
  private readonly consumers: Map<string, SqsConsumerHandler> = new Map();
  private readonly pollingIntervals: Map<string, NodeJS.Timeout> = new Map();

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
    private readonly sqsClient: SQSClient,
    private readonly sqsService: SqsService,
    private readonly config: ConfigService,
  ) {}

  async onModuleInit() {
    if (this.config.get('ENABLE_SQS_CONSUMER') !== 'true') {
      console.warn('SQS consumer is disabled');
      return;
    }

    await this.discoverConsumers();
    this.startAllConsumers();
  }

  private async discoverConsumers() {
    const providers = this.discoveryService.getProviders();

    const consumerHandlers = providers
      .filter(
        (wrapper) =>
          wrapper.instance && Object.getPrototypeOf(wrapper.instance),
      )
      .flatMap((wrapper: InstanceWrapper) => {
        const { instance } = wrapper;
        const prototype = Object.getPrototypeOf(instance);

        return this.metadataScanner
          .scanFromPrototype(instance, prototype, (methodName: string) =>
            this.extractConsumerMetadata(instance, methodName),
          )
          .filter(Boolean);
      });

    // Register only the first handler for each queue
    consumerHandlers.forEach((handler) => {
      const { topic, options } = handler;
      const queueName = topic;

      if (this.consumers.has(queueName)) {
        const existingHandler = this.consumers.get(queueName);
        this.logger.error(
          `Multiple handlers found for queue: ${queueName}. ` +
            `Using only the first handler (${existingHandler.instance.constructor.name}.${existingHandler.methodName}) ` +
            `and ignoring ${handler.instance.constructor.name}.${handler.methodName}.`,
        );
        setTimeout(() => {
          process.exit(1);
        }, 5_000);
      } else {
        this.consumers.set(queueName, handler);
        this.logger.log(
          `Discovered SQS consumer for queue: ${queueName}, method: ${handler.methodName}`,
        );
      }
    });
  }

  private extractConsumerMetadata(
    instance: any,
    methodName: string,
  ): SqsConsumerHandler | null {
    const metadata = this.reflector.get(
      SQS_CONSUMER_OPTIONS,
      instance[methodName],
    );

    if (!metadata) {
      return null;
    }

    const { topic, options } = metadata;

    return {
      instance,
      methodName,
      topic,
      options,
    };
  }

  private startAllConsumers() {
    for (const [queueName, handler] of this.consumers.entries()) {
      this.startConsumer(queueName, handler);
    }
  }

  private async startConsumer(queueName: string, handler: SqsConsumerHandler) {
    const options = handler.options;

    try {
      const queueUrl = await this.sqsService.ensureQueueExists({
        queueName,
        maxReceiveCount: options.maxRetries,
        deadLetterQueueName: options.deadLetterQueueName,
      });

      const pollMessages = async () => {
        try {
          const command = new ReceiveMessageCommand({
            QueueUrl: queueUrl,
            MaxNumberOfMessages: options.batchSize || 10,
            WaitTimeSeconds: options.waitTimeSeconds || 20,
            VisibilityTimeout: options.visibilityTimeout || 30,
            AttributeNames: ['All'],
            MessageAttributeNames: ['All'],
          });

          const response = await this.sqsClient.send(command);

          if (response.Messages && response.Messages.length > 0) {
            this.logger.debug(
              `Received ${response.Messages.length} messages from ${queueName}`,
            );

            await Promise.all(
              response.Messages.map(async (message) => {
                try {
                  const parsedBody = JSON.parse(
                    message.Body,
                  ) as BaseQueueMessage<any, any>;
                  const sqsMessage: BaseQueueMessage<any, any> = {
                    metadata: parsedBody.metadata,
                    payload: parsedBody.payload,
                    awsAttributes: {
                      eventSource: 'sqs',
                      messageId: message.MessageId,
                      receiptHandle: message.ReceiptHandle,
                      attributes: message.Attributes || {},
                      messageAttributes: message.MessageAttributes || {},
                      md5OfBody: message.MD5OfBody,
                    },
                  };

                  // Process the message with the registered handler for this queue
                  await handler.instance[handler.methodName](sqsMessage);

                  // Delete the message if autoDelete is enabled (default)
                  if (options.autoDelete !== false) {
                    await this.sqsClient.send(
                      new DeleteMessageCommand({
                        QueueUrl: queueUrl,
                        ReceiptHandle: message.ReceiptHandle,
                      }),
                    );
                  }
                } catch (error) {
                  this.logger.error(
                    `Error processing SQS message: ${error.message}`,
                    error.stack,
                  );
                }
              }),
            );
          }
        } catch (error) {
          this.logger.error(
            `Error polling SQS queue ${queueName}: ${error.message}`,
            error.stack,
          );
        }

        // Continue polling
        this.pollingIntervals.set(queueName, setTimeout(pollMessages, 100));
      };

      // Start polling
      pollMessages();
      this.logger.log(`Started SQS consumer for queue: ${queueName}`);
    } catch (error) {
      this.logger.error(
        `Failed to start SQS consumer for queue ${queueName}: ${error.message}`,
        error.stack,
      );
    }
  }

  public async stopConsumer(queueName: string) {
    if (this.pollingIntervals.has(queueName)) {
      clearTimeout(this.pollingIntervals.get(queueName));
      this.pollingIntervals.delete(queueName);
      this.logger.log(`Stopped SQS consumer for queue: ${queueName}`);
    }
  }

  public async stopAllConsumers() {
    for (const queueName of this.pollingIntervals.keys()) {
      await this.stopConsumer(queueName);
    }
  }

  public getDiscoveredQueues(): string[] {
    return Array.from(this.consumers.keys());
  }
}
