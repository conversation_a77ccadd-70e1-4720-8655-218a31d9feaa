import { SNSClient } from '@aws-sdk/client-sns';
import {
  HttpException,
  Injectable,
  Logger,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DiscoveryService, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { MetadataScanner } from '@nestjs/core/metadata-scanner';
import { sleep } from '@nestjs/terminus/dist/utils';

import { BaseQueueMessage } from '../../events/base.definition';
import { SNS_CONSUMER_OPTIONS, SnsConsumerHandler } from './sns.decorator';
import { SnsService } from './sns.service';

export type SnsRequestBody =
  | {
      Type: 'Notification';
      MessageId: string;
      TopicArn: string;
      Message: string;
      Timestamp: string;
      SignatureVersion: string;
      Signature: string;
      SigningCertURL: string;
      UnsubscribeURL: string;
    }
  | {
      Type: 'SubscriptionConfirmation';
      SubscribeURL: string;
      Token: string;
      TopicArn: string;
      SignatureVersion: string;
      Signature: string;
      SigningCertURL: string;
      UnsubscribeURL: string;
    };

@Injectable()
export class SnsConsumerService implements OnModuleInit {
  private readonly logger = new Logger(SnsConsumerService.name);
  private readonly consumers: Map<
    `${string}_${string}`,
    SnsConsumerHandler<any>
  > = new Map();

  // private readonly subscriptions: Map<string, string> = new Map();

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
    private readonly snsClient: SNSClient,
    private readonly snsService: SnsService,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    if (this.configService.get('ENABLE_SNS_CONSUMER') !== 'true') {
      console.warn('SNS consumer is disabled');
      return;
    }

    this.logger.log('SNS consumer is running');
    await sleep(1000);
    await this.discoverConsumers();
  }

  private async discoverConsumers() {
    const providers = this.discoveryService.getProviders();

    const consumerHandlers = providers
      .filter(
        (wrapper) =>
          wrapper.instance && Object.getPrototypeOf(wrapper.instance),
      )
      .flatMap((wrapper: InstanceWrapper) => {
        const { instance } = wrapper;
        const prototype = Object.getPrototypeOf(instance);

        return (
          this.metadataScanner
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            .scanFromPrototype(instance, prototype, (methodName: string) =>
              this.extractConsumerMetadata(instance, methodName),
            )
            .filter(Boolean)
        );
      });

    for (const handler of consumerHandlers) {
      const { topic, consumerGroup, filter } = handler;
      const key = `${topic}_${consumerGroup}` as const;
      if (this.getConsumerHandler(topic, consumerGroup)) {
        const handler = this.consumers.get(key);
        this.logger.error(
          `You can only have one consumerGroup per topic. one already here ${handler.instance.constructor.name}.${handler.methodName}`,
        );
        setTimeout(() => {
          process.exit(1);
        }, 5_000);

        return;
      }
      this.consumers.set(key, handler);

      // wait for server to start before registring consumers in sns
      setTimeout(() => {
        void this.snsService
          .subscribeToTopic({
            topic,
            consumerGroup,
            filter,
          })
          .catch((error) => {
            this.logger.error(
              `Error subscribing to topic ${topic} for consumerGroup ${consumerGroup}: ${error}`,
            );
            setTimeout(() => {
              process.exit(1);
            }, 5_000);
          });
        this.logger.log(
          `Discovered SNS consumeGroup: ${consumerGroup} for topic: ${topic}.`,
        );
      }, 5_000);
    }
  }

  private extractConsumerMetadata(
    instance: any,
    methodName: string,
  ): SnsConsumerHandler<any> | null {
    const metadata = this.reflector.get(
      SNS_CONSUMER_OPTIONS,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      instance[methodName],
    );

    if (!metadata) {
      return null;
    }

    const { topic, consumerGroup, filter, options } = metadata;

    return {
      instance,
      methodName,
      consumerGroup,
      topic,
      filter,
      options,
    };
  }

  public getConsumerHandler(topic: string, consumerGroup: string) {
    const key = `${topic}_${consumerGroup}` as const;
    return this.consumers.get(key);
  }

  public async processMessage(
    topic: string,
    consumerGroup: string,
    body: Extract<SnsRequestBody, { Type: 'Notification' }>,
  ) {
    try {
      const handler = this.getConsumerHandler(topic, consumerGroup);

      if (!handler) {
        this.logger.warn(`No handler found for topic: ${topic}`);
        throw new HttpException('No handler found', 404);
      }

      const _data = JSON.parse(body.Message);

      const snsMessage = {
        metadata: _data.metadata,
        payload: _data.payload,
        awsAttributes: {
          eventSource: 'sns',
          messageId: body.MessageId,
          topicArn: body.TopicArn,
          timestamp: body.Timestamp,
          signatureVersion: body.SignatureVersion,
          signature: body.Signature,
          SigningCertURL: body.SigningCertURL,
          unsubscribeURL: body.UnsubscribeURL,
        },
      } satisfies BaseQueueMessage<any, any>;

      // Handle different filter types
      if (handler.filter !== '*') {
        if (typeof handler.filter === 'function') {
          // @ts-expect-error we don't know the event type here just yet
          if (handler.filter(snsMessage) !== true) {
            return;
          }
        } else if (Array.isArray(handler.filter)) {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
          if (!handler.filter.includes(snsMessage.payload.event)) {
            return;
          }
        } else {
          this.logger.error(
            `Filter must be a function, an array of strings, or '*'. Got ${typeof handler.filter}`,
          );
          return;
        }
      }

      await handler.instance[handler.methodName](snsMessage);
      this.logger.verbose(
        `Processed message from topic: ${topic} for consumerGroup: ${consumerGroup}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing message for topic: ${topic} - consumerGroup: ${consumerGroup}`,
        error,
      );
      throw new HttpException('Error processing message', 500);
    }
  }

  async onModuleDestroy() {}

  public getDiscoveredTopics(): string[] {
    return Array.from(this.consumers.keys());
  }
}
