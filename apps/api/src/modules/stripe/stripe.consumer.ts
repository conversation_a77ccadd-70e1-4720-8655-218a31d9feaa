import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { Injectable, Logger } from '@nestjs/common';

import { StripeInvoiceUpdatedQueueEvent } from '../shared/events/invoice-topic.definition';
import { ChargeUpdatedQueueEvent } from '../shared/events/stripe-topic.definition';
import { TrackChargeRefundedUseCase } from './use-cases/track-charge-refunded.use-case';
import { StripeTrackInvoicePaidUseCase } from './use-cases/track-invoice-paid.use-case';
import { StripeTrackInvoicePaymentFailedUseCase } from './use-cases/track-invoice-payment-failed.use-case';
import { StripeTrackInvoiceUncollectibleUseCase } from './use-cases/track-invoice-uncollectible.use-case';

@Injectable()
export class StripeConsumer {
  private readonly logger = new Logger(StripeConsumer.name);

  constructor(
    private readonly stripeTrackInvoicePaidUseCase: StripeTrackInvoicePaidUseCase,
    private readonly stripeTrackInvoicePaymentFailedUseCase: StripeTrackInvoicePaymentFailedUseCase,
    private readonly stripeTrackInvoiceUncollectibleUseCase: StripeTrackInvoiceUncollectibleUseCase,
    private readonly stripeTrackchargeRefundedUseCase: TrackChargeRefundedUseCase,
  ) {}

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'collect-new-invoice',
    filter: ['finalized'],
  })
  async attempCollectNewInvoice({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'finalized') return;
    this.logger.log('invoice finialized', payload.invoice.id);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-paid',
    filter: ['paid'],
  })
  async trackInvoicePaid({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'paid') return;

    await this.stripeTrackInvoicePaidUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-payment-failed',
    filter: ['payment_failed'],
  })
  async trackInvoicePaymentFailed({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'payment_failed') return;
    await this.stripeTrackInvoicePaymentFailedUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-uncollectible',
    filter: ['uncollectible', 'voided'],
  })
  async trackInvoiceUncollectible({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'uncollectible' && payload.event !== 'voided') return;
    await this.stripeTrackInvoiceUncollectibleUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'charge-updated',
    consumerGroup: 'track-charge-refunded',
    filter: ['refunded'],
  })
  async trackChargeRefunded({ payload }: ChargeUpdatedQueueEvent) {
    if (payload.event !== 'refunded') return;
    await this.stripeTrackchargeRefundedUseCase.execute(payload.charge);
  }
}
