import { createHash } from 'crypto';
import * as process from 'process';
import { CacheService } from '@modules/cache/cache.service';
import { Injectable, Logger } from '@nestjs/common';
import { Stripe } from 'stripe';

type InvoiceItem = {
  prescriptionId: string;
  type: 'product';
  priceId: string;
  quantity: number;
  couponId?: string | null;
};
// | {
//     prescriptionId: string;
//     type: 'other';
//     amountInCents: number;
//     description: string;
//     couponId?: string | null;
//   };

export type DraftInvoice = {
  customerId: string;
  description?: string;
  metadata?: Partial<Record<InvoiceMetadataKey, string | string[]>>;
  items: InvoiceItem[];
};

type InvoiceMetadataKey =
  | 'internalInvoiceId'
  | 'treatmentIdList'
  | 'prescriptionIdList'
  | 'subscriptionName'
  | (string & {});

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private readonly stripe: Stripe;

  constructor(private readonly cacheService: CacheService) {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16',
    });
  }

  client() {
    return this.stripe;
  }

  async getCustomer(customerId: string) {
    try {
      return (await this.stripe.customers.retrieve(
        customerId,
      )) as Stripe.Customer;
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async createCustomer(
    email: string,
    name: string,
    phone: string,
    address: Stripe.AddressParam,
    shipping: Stripe.CustomerCreateParams.Shipping,
  ) {
    const customer = await this.stripe.customers.list({
      email: email,
      limit: 1,
    });
    if (customer?.data?.length > 0) {
      return customer.data[0];
    }

    return await this.stripe.customers.create({
      name,
      email,
      phone,
      address,
      shipping,
    });
  }

  async updateBillingAddress(customerId: string, address: Stripe.AddressParam) {
    try {
      return await this.stripe.customers.update(customerId, { address });
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async updateShippingAddress(
    customerId: string,
    name: string,
    address: Stripe.AddressParam,
  ) {
    try {
      return await this.stripe.customers.update(customerId, {
        shipping: { name, address },
      });
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async updateCustomer(
    customerId: string,
    attributes: Stripe.CustomerUpdateParams,
  ) {
    try {
      return await this.stripe.customers.update(customerId, attributes);
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async createPaymentSetupIntent(customerId: string) {
    try {
      return await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
      });
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async createExpressCheckoutIntent({
    customerId,
    amountInCents,
  }: {
    customerId: string;
    amountInCents: number;
  }) {
    try {
      return await this.stripe.paymentIntents.create({
        customer: customerId,
        amount: amountInCents,
        currency: 'usd',
        payment_method_types: ['card'],
      });
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async creditCustomer(
    customerId: string,
    data: {
      amountInDollars: number;
      description: string;
      metadata: Record<string, any>;
    },
  ) {
    if (data.amountInDollars <= 0) {
      throw new Error('Amount must be greater than 0');
    }
    await this.stripe.customers.createBalanceTransaction(customerId, {
      amount: data.amountInDollars * 100 * -1,
      currency: 'usd',
      description: data.description,
      metadata: data.metadata,
    });
  }

  async checkClientSecretValidity(clientSecret: string): Promise<boolean> {
    try {
      const paymentIntentId = clientSecret.split('_secret_')[0];
      const paymentIntent =
        await this.stripe.setupIntents.retrieve(paymentIntentId);

      return !['canceled', 'succeeded', 'expired'].includes(
        paymentIntent.status,
      );
      // Client secret is still valid
    } catch (error) {
      this.logger.error('Error checking client secret validity:', error);
      return false;
    }
  }

  async setPaymentMethodAsDefault(
    stripeCustomerId: string,
    paymentMethodId: string,
  ) {
    try {
      await this.stripe.customers.update(stripeCustomerId, {
        invoice_settings: { default_payment_method: paymentMethodId },
      });
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async payInvoice(invoiceId: string) {
    try {
      return await this.stripe.invoices.pay(invoiceId);
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async attemptInvoiceCollect(invoiceId: string) {
    try {
      return await this.stripe.invoices.pay(invoiceId);
    } catch (e) {
      this.logger.error('Error sending invoice:', e);
    }
  }

  async voidInvoice(invoiceId: string) {
    try {
      return await this.stripe.invoices.voidInvoice(invoiceId);
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async sendVoidInvoice(invoiceId: string) {
    try {
      return await this.stripe.invoices.del(invoiceId);
    } catch (e) {
      this.logger.error('Error voiding invoice:', e);
    }
  }
  async deletDraft(invoiceId: string) {
    try {
      return await this.stripe.invoices.del(invoiceId);
    } catch (e) {
      this.logger.error('Error voiding invoice:', e);
    }
  }

  async createInvoice(
    customerId: string,
    opts: {
      description?: string;
      metadata?: Partial<Record<InvoiceMetadataKey, string | string[]>>;
    } = {},
  ) {
    try {
      return await this.stripe.invoices.create({
        customer: customerId,
        description: opts.description,
        metadata: opts.metadata
          ? this.serializeMetadata(opts.metadata)
          : undefined,
      });
    } catch (e) {
      this.logger.error('Error creating invoice:', e);
      throw new Error((e as Error).message);
    }
  }

  async getProducts() {
    try {
      const products = await this.stripe.products.list({
        limit: 100,
        expand: ['data.default_price'],
      });
      const prices = await this.stripe.prices.list({ limit: 100 });
      // @todo this should work for a long time, _but_ if we have more than 100 products or prices, we need to paginate

      return { products, prices };
    } catch (e) {
      throw new Error((e as Error).message);
    }
  }

  async retrieveCharge(chargeId: string) {
    try {
      return await this.stripe.charges.retrieve(chargeId);
    } catch (e) {
      // swallow the exception and return null since this is not a critical operation and used only for tracking
      // throw new Error((e as Error).message);
      this.logger.error('Error retrieving charge:', e);
      return null;
    }
  }

  async applyCouponToInvoice(
    invoiceId: string,
    couponId: string,
  ): Promise<void> {
    await this.stripe.invoices.update(invoiceId, {
      discounts: [{ coupon: couponId }],
    });
  }

  async createInvoiceItem(
    customerId: string,
    invoiceId: string,
    priceId: string,
    quantity: number = 1,
    couponId?: string | null,
  ): Promise<Stripe.InvoiceItem> {
    return await this.stripe.invoiceItems.create({
      customer: customerId,
      invoice: invoiceId,
      price: priceId,
      quantity,
      ...(couponId && { discounts: [{ coupon: couponId }] }),
    });
  }

  async addInvoiceItem({
    invoiceId,
    customerId,
    item,
    couponId = null,
  }: {
    invoiceId: string;
    customerId: string;
    item: { type: 'product'; priceId: string; quantity: number };
    // | { type: 'other'; amountInCents: number; description: string };
    couponId?: string | null;
  }) {
    return this.stripe.invoiceItems.create({
      customer: customerId,
      invoice: invoiceId,
      price: item.priceId,
      quantity: item.quantity,
      ...(couponId && { discounts: [{ coupon: couponId }] }),
    });
  }

  async retrieveInvoice(invoiceId: string): Promise<Stripe.Invoice> {
    try {
      return await this.stripe.invoices.retrieve(invoiceId);
    } catch (error) {
      throw new Error(
        `Failed to retrieve invoice ${invoiceId}: ${error.message}`,
      );
    }
  }

  async refundInvoice(invoiceId: string): Promise<Stripe.Refund> {
    try {
      const invoice = await this.retrieveInvoice(invoiceId);

      if (!invoice.charge) {
        throw new Error(`Invoice ${invoiceId} has no associated charge`);
      }

      return await this.stripe.refunds.create({
        charge: invoice.charge as string,
        reason: 'requested_by_customer',
      });
    } catch (error) {
      throw new Error(
        `Failed to refund invoice ${invoiceId}: ${error.message}`,
      );
    }
  }

  async getCoupon(couponId: string) {
    const coupon = await this.cacheService.remember(
      `stripe_coupon:${couponId}`,
      3600000, // 1 hour
      async () => this.stripe.coupons.retrieve(couponId),
    );

    if (!coupon) {
      throw new Error('Coupon not found');
    }

    return coupon;
  }

  async retrievePaymentIntent(
    paymentIntentId: string,
  ): Promise<Stripe.PaymentIntent | null> {
    try {
      return await this.stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (error) {
      this.logger.error(
        `Error retrieving payment intent ${paymentIntentId}:`,
        error,
      );
      return null;
    }
  }

  async retrieveInvoiceWithLineItems(
    invoiceId: string,
  ): Promise<Stripe.Invoice | null> {
    try {
      return await this.stripe.invoices.retrieve(invoiceId, {
        expand: ['lines.data.price.product'],
      });
    } catch (error) {
      this.logger.error(
        `Error retrieving invoice with line items ${invoiceId}:`,
        error,
      );
      return null;
    }
  }

  async listInvoices({
    customerId,
    limit = 100,
    fromDate,
    metadata,
    toDate,
    patientId,
  }: {
    customerId: string;
    fromDate?: Date;
    toDate?: Date;
    status?: Stripe.Invoice.Status | Stripe.Invoice.Status[];
    metadata?: Partial<
      Record<
        InvoiceMetadataKey,
        { equals: string | string[] } | { includes: string } | { exists: true }
      >
    >;
    limit?: number;
    patientId?: string;
  }) {
    let query = `customer:"${customerId}"`;
    if (patientId) {
      query += ` AND metadata["patientId"]:"${patientId}"`;
    }

    if (fromDate)
      query += ` AND created>${Math.floor(fromDate.getTime() / 1000)}`;

    if (toDate) query += ` AND created<${Math.floor(toDate.getTime() / 1000)}`;

    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        if ('equals' in value) {
          query += ` AND metadata["${key}"]:"${Array.isArray(value.equals) ? value.equals.join(',') : value.equals}"`;
        } else if ('includes' in value) {
          query += ` AND metadata["${key}"]~"${value.includes}"`;
        } else if ('exists' in value && value.exists) {
          query += ` AND -metadata["${key}"]:null`;
        }
      });
    }

    this.logger.debug('Query:', query);

    return this.stripe.invoices.search({ query: query, limit });
  }

  private serializeMetadata(
    metadata: Record<string, string | string[]>,
  ): Record<string, string> {
    return Object.fromEntries(
      Object.entries(metadata).map(([key, value]) => [
        key,
        Array.isArray(value) ? value.join(',') : value,
      ]),
    );
  }

  public deteministicId(parts: string[]) {
    const id = parts.sort().join('_');
    const hash = createHash('sha256').update(id).digest('base64');
    return hash;
  }
}
