import { runInDbTransaction } from '@/helpers/transaction';
import { TreatmentUpdatedEvent } from '@/modules/shared/events/treatment-topic.definition';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  TreatmentService,
  TreatmentSnapshot,
} from '@modules/treatment/services/treatment.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UpdateTreatmentsJob {
  private readonly BATCH_SIZE = 10;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly treatmentService: TreatmentService,
  ) {}

  async run(): Promise<void> {
    const treatments = await this.prismaService.treatment.findMany({
      where: { nextEventIn: { not: null, lte: new Date() } },
      select: { id: true, state: true, status: true },
      orderBy: { nextEventIn: 'asc' },
      take: this.BATCH_SIZE,
    });
    for (const treatment of treatments) {
      try {
        await runInDbTransaction(this.prismaService, async (prisma) => {
          const treatmentEventsToEmit: {
            event: TreatmentUpdatedEvent['event'];
          }[] = [];
          const actor = await this.treatmentService.getActor(
            treatment.id,
            (e) => {
              treatmentEventsToEmit.push(e);
              console.log('[UpdateTreatmentsJob] emitTreatmentEvent', e);
            },
            treatment.state as unknown as TreatmentSnapshot,
          );
          const snapshot = actor.getSnapshot();
          if (snapshot.can({ type: 'next' })) {
            actor.send({ type: 'next' });
            await this.treatmentService.updateTreatmentRecord(actor, {
              prisma,
            });
          }
          for (const { event } of treatmentEventsToEmit) {
            console.log(
              '[UpdateTreatmentsJob] emitTreatmentEvent',
              event,
              treatment.id,
            );
            await this.treatmentService.emitTreatmentUpdatedEvent(
              event,
              treatment.id,
              { prisma },
            );
          }
        });
      } catch (error) {
        console.error(`Error updating treatment ${treatment.id}:`, error);
        await this.prismaService.treatment.update({
          where: { id: treatment.id },
          data: { nextEventIn: null },
        });
      }
    }
  }
}
