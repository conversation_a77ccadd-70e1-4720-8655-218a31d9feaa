import { runInDbTransaction } from '@/helpers/transaction';
import { OutboxerService } from '@/modules/shared/outboxer/outboxer.service';
import { SubscriptionService } from '@/modules/subscription/subscription.service';
import { AuditService } from '@modules/audit-log/audit-log.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  DraftInvoice,
  StripeService,
} from '@modules/stripe/service/stripe.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as dayjs from 'dayjs';

type QueuedPrescription = {
  id: string;
  treatmentId: string;
  productPrice: { id: string };
  stripeCouponId: string | null;
  patient: Patient;
  treatment: { vials: number; couponCode: string | null };
};
type Patient = {
  id: string;
  stripeCustomerId: string | null;
  cancelationDiscount: 'notUsed' | 'requested' | 'alreadyUsed' | null;
  promoCoupon: string | null;
};

@Injectable()
export class ProcessDraftPrescriptionsJob {
  private readonly logger = new Logger(ProcessDraftPrescriptionsJob.name);

  private readonly BATCH_SIZE = 50;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly stripeService: StripeService,
    private readonly configService: ConfigService,
    private readonly auditService: AuditService,
    private readonly outboxer: OutboxerService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async run() {
    const subscriptionProducts =
      await this.subscriptionService.getSubscriptionProducts();
    const subscriptionProductIds = subscriptionProducts.map(
      (product) => product.id,
    );

    const allPrescriptions = await this.prismaService.prescription.findMany({
      where: {
        status: 'queued',
        stripeInvoiceId: null,
        createdAt: { lt: dayjs().subtract(30, 'second').toDate() },
      },
      select: {
        id: true,
        patientId: true,
        stripeInvoiceId: true,
        pharmacy: true,
        productId: true,
      },
      orderBy: { patientId: 'asc' },
      take: this.BATCH_SIZE,
    });

    const invoicablePrescriptions = allPrescriptions.filter(
      (p) => !subscriptionProductIds.includes(p.productId),
    );

    if (invoicablePrescriptions.length === 0) return;

    // const withInvoice = invoicablePrescriptions.filter(
    //   (p) => p.stripeInvoiceId,
    // );
    // const withoutInvoice = invoicablePrescriptions.filter(
    //   (p) => !p.stripeInvoiceId,
    // );

    // if (withInvoice.length > 0) {
    //   this.logger.error(
    //     `!!! Found ${withInvoice.length} prescriptions queued with existing invoices`,
    //   );
    // }

    const withoutInvoiceGroups = this.groupBy(allPrescriptions, 'patientId');
    for (const [patientId] of Object.entries(withoutInvoiceGroups)) {
      await this.createNewInvoice(patientId);
    }
  }

  private async createNewInvoice(patientId: string): Promise<void> {
    this.logger.debug(`Processing prescriptions for patient ${patientId}`);

    const queuedPrescriptions: QueuedPrescription[] =
      await this.prismaService.prescription.findMany({
        where: { patientId: patientId, status: 'queued' },
        select: {
          id: true,
          treatmentId: true,
          productPrice: { select: { id: true } },
          stripeCouponId: true,
          patient: {
            select: {
              id: true,
              stripeCustomerId: true,
              cancelationDiscount: true,
              promoCoupon: true,
            },
          },
          treatment: { select: { vials: true, couponCode: true } },
        },
      });
    if (queuedPrescriptions.length === 0) return;

    const patient = queuedPrescriptions[0].patient;

    const prescriptionIds = queuedPrescriptions.map((p) => p.id).sort();
    const treatmentIds = [
      ...new Set(queuedPrescriptions.map((p) => p.treatmentId)),
    ].sort();

    const invoiceCoupon = await this.determineInvoiceCoupon(
      queuedPrescriptions,
      patient,
    );

    const internalInvoiceId =
      this.stripeService.deteministicId(prescriptionIds);

    const draftInvoice: DraftInvoice = {
      customerId: patient.stripeCustomerId,
      metadata: {
        patientId: patient.id,
        internalInvoiceId: internalInvoiceId,
        treatmentIdList: treatmentIds,
        prescriptionIdList: prescriptionIds,
      },
      items: queuedPrescriptions.map((prescription) => ({
        type: 'product',
        prescriptionId: prescription.id,
        priceId: prescription.productPrice.id,
        quantity: prescription.treatment.vials,
        couponId: invoiceCoupon ? null : prescription.stripeCouponId,
      })),
    };

    await runInDbTransaction(this.prismaService, async (prisma) => {
      await prisma.prescription.updateMany({
        where: { id: { in: prescriptionIds } },
        data: { status: 'open', stripeCouponId: invoiceCoupon },
      });

      await this.outboxer.enqueue(
        patientId,
        'stripe-invoice-updated',
        {
          event: 'create',
          patientId,
          draftInvoice,
        },
        {
          prisma,
          deduplicationId: internalInvoiceId,
          deduplicationPeriodInSeconds: 432_000, // 5 days
        },
      );
    });
  }

  private async determineInvoiceCoupon(
    prescriptions: QueuedPrescription[],
    patient: Patient,
  ): Promise<string | null> {
    // First get all relevant treatments
    const treatments = await this.prismaService.treatment.findMany({
      where: { id: { in: prescriptions.map((p) => p.treatmentId) } },
      select: { id: true, couponCode: true, vials: true },
    });

    // First priority: Check treatments for vial bulk discounts
    const maxVials = treatments.reduce(
      (max, treatment) => Math.max(max, treatment.vials),
      0,
    );
    const discountCode =
      maxVials === 3
        ? process.env.STRIPE_3_MONTHS_DISCOUNT_CODE
        : maxVials === 6
          ? process.env.STRIPE_6_MONTHS_DISCOUNT_CODE
          : null;
    if (discountCode) return discountCode;

    // Second priority: Check treatments for coupon codes
    const treatmentCoupons = treatments
      .map((t) => t.couponCode)
      .filter(Boolean);

    if (
      treatmentCoupons.length > 0 &&
      treatmentCoupons.length === treatments.length &&
      new Set(treatmentCoupons).size === 1
    ) {
      await this.prismaService.treatment.updateMany({
        where: { id: { in: treatments.map((t) => t.id) } },
        data: { couponCode: null },
      });
      return treatmentCoupons[0];
    }

    // Third priority: Check prescriptions for stripe coupon ids
    const prescriptionCoupons = prescriptions
      .map((p) => p.stripeCouponId)
      .filter(Boolean);

    if (
      prescriptionCoupons.length > 0 &&
      prescriptionCoupons.length === prescriptions.length &&
      new Set(prescriptionCoupons).size === 1
    ) {
      return prescriptionCoupons[0];
    }

    // Forth priority: Check for cancellation discount
    if (patient.cancelationDiscount === 'requested') {
      await this.prismaService.patient.update({
        where: { id: patient.id },
        data: { cancelationDiscount: 'alreadyUsed' },
      });
      return this.configService.get<string>('STRIPE_CANCELATION_DISCOUNT_CODE');
    }

    // Last priority: Check for promo coupon
    if (patient.promoCoupon) {
      await this.prismaService.patient.update({
        where: { id: patient.id },
        data: { promoCoupon: null },
      });
      return patient.promoCoupon;
    }

    return null;
  }

  private groupBy<T>(array: T[], key: keyof T): { [key: string]: T[] } {
    return array.reduce(
      (result, currentValue) => {
        const groupKey = currentValue[key] as string;
        (result[groupKey] = result[groupKey] || []).push(currentValue);
        return result;
      },
      {} as { [key: string]: T[] },
    );
  }
}
