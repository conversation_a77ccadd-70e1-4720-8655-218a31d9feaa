import { SESClient } from '@aws-sdk/client-ses';
import { AppCacheModule } from '@modules/cache/cache.module';
import { DoctorModule } from '@modules/doctor/doctor.module';
import { DosespotModule } from '@modules/dosespot/dosespot.module';
import { IntegrationsModule } from '@modules/integrations/integrations.module';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { SharedModule } from '@modules/shared/shared.module';
import { TreatmentEventListeners } from '@modules/treatment/events';
import { TreatmentInvoiceUncollectibleEvent } from '@modules/treatment/events/treatment-invoice-uncollectible.event';
import { TreatmentPatientCancelledEventListener } from '@modules/treatment/events/treatment-patient-cancelled.event';
import { TreatmentPaymentFailedEvent } from '@modules/treatment/events/treatment-payment-failed.event';
import { TreatmentPaymentSuccessEvent } from '@modules/treatment/events/treatment-payment-success.event';
import { TreatmentPrescriptionCreateEventListener } from '@modules/treatment/events/treatment-prescription-create.event';
import { TreatmentJobs } from '@modules/treatment/jobs';
import { TreatmentQueue } from '@modules/treatment/treatment.queue';
import { TreatmentUseCases } from '@modules/treatment/use-cases';
import { TreatmentCancelUseCase } from '@modules/treatment/use-cases/treatment-cancel.use-case';
import { forwardRef, Module } from '@nestjs/common';

import { AdminService } from '../admin/admin.service';
import { CreateAdminAccountUseCase } from '../admin/use-cases/create-admin-account-use.case';
import { AuditLogModule } from '../audit-log/audit-log.module';
import { AuthModule } from '../auth/auth.module';
import { ChatModule } from '../chat/chat.module';
import { FollowUpModule } from '../follow-up/follow-up.module';
import { PatientModule } from '../patient/patient.module';
import { SesService } from '../shared/aws/ses/ses.service';
import { SnsModule } from '../shared/aws/sns/sns.module';
import { OrchestrationModule } from '../shared/orchestration/orchestration.module';
import { OutboxerModule } from '../shared/outboxer/outboxer.module';
import { StripeModule } from '../stripe/stripe.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { TreatmentLockingService } from './services/treatment-locking.service';
import { TreatmentRefundService } from './services/treatment-refund.service';
import { TreatmentService } from './services/treatment.service';
import { TreatmentSubscriptionWorker } from './treatment-subscription.worker';
import { TreatmentController } from './treatment.controller';

@Module({
  imports: [
    StripeModule,
    PrismaModule,
    DoctorModule,
    DosespotModule,
    FollowUpModule,
    AuditLogModule,
    OutboxerModule,
    SnsModule,
    forwardRef(() => PatientModule),
    forwardRef(() => ChatModule),
    IntegrationsModule,
    SharedModule,
    AppCacheModule,
    SubscriptionModule,
    OrchestrationModule,
    forwardRef(() => AuthModule),
  ],
  controllers: [TreatmentController],
  providers: [
    TreatmentService,
    TreatmentLockingService,
    TreatmentRefundService,
    AdminService,
    CreateAdminAccountUseCase,
    TreatmentPrescriptionCreateEventListener,
    TreatmentQueue,
    SesService,
    SESClient,
    TreatmentSubscriptionWorker,
    ...TreatmentUseCases,
    ...TreatmentEventListeners,
    ...TreatmentJobs,
  ],
  exports: [
    TreatmentPaymentSuccessEvent,
    TreatmentPaymentFailedEvent,
    TreatmentInvoiceUncollectibleEvent,
    TreatmentPatientCancelledEventListener,
    TreatmentService,
    TreatmentLockingService,
    TreatmentRefundService,
    TreatmentCancelUseCase,
    ...TreatmentEventListeners,
  ],
})
export class TreatmentModule {}
