import { OrchestrationService } from '@modules/shared/orchestration/orchestration.service';
import { ProcessDraftPrescriptionsJob } from '@modules/treatment/jobs/process-draft-prescriptions.job';
import { SendNotificationsJob } from '@modules/treatment/jobs/send-notifications.job';
import { UpdateTreatmentsJob } from '@modules/treatment/jobs/update-treatments.job';
import { VerifyPendingTreatmentsJob } from '@modules/treatment/jobs/verify-pending-treatments.job';
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class TreatmentQueue {
  private readonly disabled: boolean;
  private readonly logger = new Logger(TreatmentQueue.name);

  constructor(
    private readonly updateTreatmentsJob: UpdateTreatmentsJob,
    private readonly processDraftPrescriptionsJob: ProcessDraftPrescriptionsJob,
    private readonly sendNotificationsJob: SendNotificationsJob,
    private readonly verifyPendingTreatmentsJob: VerifyPendingTreatmentsJob,
    private readonly orchestrationService: OrchestrationService,
  ) {
    // Check IS_CLI first, if true, disable this queue
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);

    if (this.disabled) {
      setTimeout(() => {
        console.warn('⚠️ ⚠️ ⚠️ Treatment Queues disabled!  ⚠️ ⚠️ ⚠️');
      }, 3000);
    }
  }

  @Cron('*/10 * * * * *') // every 10 seconds
  async updateTreatments(): Promise<void> {
    if (this.disabled) return;
    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'update-treatments-cron',
          ttl: 10_000,
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.updateTreatmentsJob.run();
        },
      );
    } catch (error: any) {
      this.logger.error(
        `error in treatment job updateTreatments ${error.message}`,
      );
      throw error;
    }
  }

  @Cron('*/30 * * * * *') // every 30 seconds
  async processDraftPrescriptions(): Promise<void> {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'process-draft-prescriptions-cron',
          ttl: 30_000,
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.processDraftPrescriptionsJob.run();
        },
      );
    } catch (error: any) {
      console.log(
        `error in treatment job processDraftPrescriptions ${error.message}`,
      );
      throw error;
    }
  }

  @Cron('0 */5 * * * *') // every 5 minutes
  async sendNotifications(): Promise<void> {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'send-notifications-cron',
          ttl: 60_000,
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.sendNotificationsJob.run();
        },
      );
    } catch (error: any) {
      console.log(`error in treatment job sendNotifications ${error.message}`);
      throw error;
    }
  }

  @Cron('0 */10 * * * *') // every 10 minutes
  async verifyPendingTreatments(): Promise<void> {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'verify-pending-treatments-cron',
          ttl: 60_000,
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.verifyPendingTreatmentsJob.run();
        },
      );
    } catch (error: any) {
      console.log(
        `error in treatment job verifyPendingTreatmentsJob ${error.message}`,
      );
      throw error;
    }
  }
}
