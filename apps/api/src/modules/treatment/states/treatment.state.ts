import * as process from 'node:process';
import { TreatmentUpdatedEvent } from '@/modules/shared/events/treatment-topic.definition';
// Removed MappingSource import as it no longer exists
import * as dayjs from 'dayjs';
import { and, assign, enqueueActions, not, or, setup, stateIn } from 'xstate';

export type RefillSystem = 'static' | 'scaling' | 'downscaling';

export type TreatmentMachineInput = {
  patientId: string;
  treatmentId: string;
  delayUntil?: string;
  isCore: boolean;
  pharmacy: string;
  refills: number;
  vials: number;
  refillSystem: RefillSystem;
  productPricesList: TreatmentProduct[];
};

export type TreatmentMachineContext = {
  patientId: string;
  treatmentId: string;
  pharmacy: string;
  vials: number;
  refillSystem: RefillSystem;
  activeProduct?: Omit<TreatmentProduct, 'endDate' | 'startDate'> & {
    startDate: string;
  };
  products: TreatmentProduct[];
  isCore: boolean;
  currentRefill: number;
  refills: number;
  nextEventIn: string | null;
  nextNotificationIn: string | null;
  notificationSent: { nextDose: boolean; nextRefill: boolean };
  nextRefillDate: string | null;
  endOfLastRefillDate: string | null;
  delayUntil: string | null;
  inProgressSince: string | null;
  lastChargedAt: string | null;
  failedAt: string | null;
  completedAt: string | null;
  cancelledAt: string | null;
  deletedAt: string | null;
  uncollectibleAt: string | null;
  pausedAt: string | null;
  inProgressSubState?: string | null;
  externalMapping?: string | null;
  transferredTo?: string | null;
};

export type TreatmentMachineEvents =
  | { type: 'start' }
  | { type: 'next' }
  | { type: 'cancel' }
  | { type: 'pause'; until?: string }
  | { type: 'chargeSucceeded'; externalMapping?: string }
  | { type: 'chargeFailed' }
  | { type: 'prescribeSucceeded' }
  | { type: 'prescribeFailed' }
  | { type: 'prescribed' }
  | { type: 'retry'; invoiceId: string }
  | { type: 'resume' }
  | { type: 'uncollectible' }
  | { type: 'fireNext' }
  | { type: 'notify' }
  | { type: 'moveRefillDate'; date: string }
  | { type: 'transfer'; treatmentId: string }
  | { type: 'shiftDateForward'; offset: number };

export type TreatmentProduct = {
  id: string;
  price: number;
  name: string;
  pharmacy: string;
  form: 'injectable' | 'oral' | 'tablet';
  dose: string;
  days: number;
  coupon?: string;
  startDate?: string;
  endDate?: string;
};

export type ActiveTreatmentProduct = Omit<TreatmentProduct, 'endDate'> & {
  startDate: string;
};

export const FIRST_PRESCRIPTION_DELAY = 2 * 60 * 1000;

export const daysLeftForNotification = {
  nextDose: 2,
  nextRefill: 2,
};

export const createTreatmentMachine = (
  emitEvent: (event: { event: TreatmentUpdatedEvent['event'] }) => void,
  persistedContext?: Partial<TreatmentMachineContext>,
) => {
  return setup({
    types: {
      context: {} as TreatmentMachineContext,
      events: {} as TreatmentMachineEvents,
      input: {} as TreatmentMachineInput,
    },
    guards: {
      canStart: ({ context }) =>
        !context.delayUntil || dayjs().isAfter(dayjs(context.delayUntil)),
      canNext: ({ context }) =>
        context.nextEventIn && dayjs().isAfter(dayjs(context.nextEventIn)),
      isTimeToNotify: ({ context }) => {
        if (!context.nextNotificationIn) return false;
        return dayjs().isAfter(dayjs(context.nextNotificationIn));
      },
      hasExternalMapping: ({ event }) => {
        return (
          event.type === 'chargeSucceeded' &&
          event.externalMapping !== null &&
          event.externalMapping !== undefined
        );
      },
      hasRefills: ({ context }) => context.refills > 0,
      isValidDateToMoveRefill: ({ context, event }) => {
        if (event.type !== 'moveRefillDate') return false;
        if (context.currentRefill === context.refills) return false;
        if (event.date === undefined) return true;
        return dayjs(event.date).isAfter(dayjs());
      },
      canShiftDateForward: () => process.env.ENVIRONMENT !== 'production',
      timeToCharge: ({ context }) => {
        if (!context.inProgressSince) return false;

        if (context.currentRefill > 0) return true;

        const now = dayjs();

        const elapsedTime = now.diff(
          dayjs(context.inProgressSince),
          'millisecond',
        );

        return elapsedTime >= FIRST_PRESCRIPTION_DELAY; // 2 minutes delay before charging
      },
      timeForNextRefill: ({ context }) => {
        if (!context.nextRefillDate) return false;
        return dayjs().isAfter(dayjs(context.nextRefillDate));
      },
      hasMoreRefills: ({ context }) => context.currentRefill < context.refills,
      isTransferred: ({ context }) => !!context.transferredTo,
    },
    actions: {
      updateNextEventInForCharging: assign({
        nextEventIn: ({ context }) =>
          dayjs()
            .add(
              context.currentRefill === 0 ? FIRST_PRESCRIPTION_DELAY : 0,
              'milliseconds',
            )
            .toISOString(),
      }),
      updateNextEventInForWaitingBetweenRefills: assign({
        nextEventIn: ({ context }) => {
          const date =
            context.currentRefill < context.refills
              ? context.nextRefillDate
              : context.endOfLastRefillDate;
          return dayjs(date).toISOString();
        },
      }),
      removeNextEventIn: assign({ nextEventIn: null }),
      removeNextNotificationIn: assign({ nextNotificationIn: null }),
      init: assign(({ context }) => {
        const firstProduct = context.products[0];
        const nextEventIn = context.delayUntil ?? dayjs().toISOString();
        const nextRefillDate = dayjs(nextEventIn)
          .add(firstProduct.days, 'day')
          .toISOString();
        const totalDays = context.products.reduce(
          (sum, product) => sum + product.days,
          0,
        );
        const endOfLastRefillDate = dayjs(nextEventIn)
          .add(totalDays, 'day')
          .toISOString();

        return { nextRefillDate, endOfLastRefillDate, nextEventIn };
      }),
      setRefillDates: assign(({ context }) => {
        const remainingDays = context.products
          .slice(context.currentRefill)
          .reduce((sum, product) => sum + product.days, 0);

        const inProgressSince =
          context.inProgressSince ?? dayjs().toISOString();
        // we recalculate this since it's possible that the refill date was moved
        const nextRefillDate = dayjs()
          .add(context.activeProduct.days, 'days')
          .toISOString();
        const endOfLastRefillDate = dayjs()
          .add(remainingDays, 'day')
          .toISOString();

        if (context.endOfLastRefillDate !== endOfLastRefillDate) {
          emitEvent({ event: 'end_date_changed' });
        }

        return { inProgressSince, nextRefillDate, endOfLastRefillDate };
      }),
      endActiveProduct: assign(({ context }) => {
        const products = [...context.products];

        products[context.currentRefill] = {
          ...products[context.currentRefill],
          endDate: dayjs().toISOString(),
        };

        return { products };
      }),
      setActiveProduct: assign(({ context }) => {
        const products = [...context.products];

        const nextActiveProduct = {
          ...products[context.currentRefill],
          startDate: dayjs().toISOString(),
        };
        products[context.currentRefill] = nextActiveProduct;

        return {
          products,
          activeProduct: nextActiveProduct,
        };
      }),
      captureExternalMapping: assign({
        externalMapping: ({ event }) => {
          if (event.type === 'chargeSucceeded') {
            return event.externalMapping;
          }
          return null;
        },
      }),
      runCharge: () => {
        emitEvent({ event: 'prescription_create' });
      },
      runPrescribe: () => {
        emitEvent({ event: 'prescription_prescribe' });
      },
      incrementRefillCounter: assign({
        currentRefill: ({ context }) => context.currentRefill + 1,
      }),
      recordPauseStart: assign(({ context, event }) => {
        if (event.type !== 'pause') return context;
        const pausedAt = dayjs().toISOString();
        const nextEventIn = event.until;

        return { nextEventIn, pausedAt };
      }),
      recordPauseEnd: assign(({ context }) => {
        const totalPausedDuration = dayjs(dayjs().toISOString()).diff(
          dayjs(dayjs(context.pausedAt)),
        );
        const endOfLastRefillDate = dayjs(context.endOfLastRefillDate)
          .add(totalPausedDuration, 'millisecond')
          .toISOString();

        emitEvent({ event: 'resumed' });

        return {
          nextRefillDate: dayjs(context.nextRefillDate)
            .add(totalPausedDuration, 'millisecond')
            .toISOString(),
          endOfLastRefillDate,
          pausedAt: null,
        };
      }),
      storeLastChargedAt: assign({
        lastChargedAt: () => dayjs().toISOString(),
        failedAt: null,
      }),
      storeFailedAt: assign({
        failedAt: () => dayjs().toISOString(),
        lastChargedAt: null,
      }),
      storeCancelledAt: assign({
        cancelledAt: () => dayjs().toISOString(),
      }),
      storeDeletedAt: assign({
        deletedAt: () => dayjs().toISOString(),
      }),
      storeCompletedAt: assign({
        completedAt: () => dayjs().toISOString(),
      }),
      storeUncollectibleAt: assign({
        uncollectibleAt: () => dayjs().toISOString(),
      }),
      emitCancelledEvent: () => {
        emitEvent({ event: 'cancelled' });
      },
      emitCompletedEvent: () => {
        emitEvent({ event: 'completed' });
      },
      emitDeleteEvent: () => {
        emitEvent({ event: 'treatment_delete' });
      },
      moveRefillDate: assign(({ context, event }) => {
        if (event.type !== 'moveRefillDate') return null;
        const newNextRefillDate = dayjs(event.date);
        // @todo ALWAYS check the dates before feeding them to dayjs, exceptions will be thrown
        const diff = newNextRefillDate.diff(dayjs(context.nextRefillDate));

        const nextRefillDate = dayjs(context.nextRefillDate)
          .add(diff)
          .toISOString();
        const endOfLastRefillDate = dayjs(context.endOfLastRefillDate)
          .add(diff)
          .toISOString();
        const nextEventIn = newNextRefillDate.toISOString();
        const nextNotificationIn = !context.nextNotificationIn
          ? context.nextNotificationIn
          : dayjs(context.nextNotificationIn).add(diff).toISOString();

        emitEvent({ event: 'end_date_changed' });

        return {
          nextRefillDate,
          endOfLastRefillDate,
          nextEventIn,
          nextNotificationIn,
        };
      }),
      shiftDateForward: assign(({ context, event }) => {
        if (event.type !== 'shiftDateForward') return context;

        const shiftAmount = event.offset * 1000; // Convert seconds to milliseconds

        const shiftDate = (date: string | null) =>
          date
            ? dayjs(date).subtract(shiftAmount, 'millisecond').toISOString()
            : null;

        // Note: we don't emit 'treatment.endDateChanged' because it's already being shifted by the main shiftDates method

        return {
          delayUntil: shiftDate(context.delayUntil),
          nextRefillDate: shiftDate(context.nextRefillDate),
          endOfLastRefillDate: shiftDate(context.endOfLastRefillDate),
          nextEventIn: shiftDate(context.nextEventIn),
          nextNotificationIn: shiftDate(context.nextNotificationIn),
          lastChargedAt: shiftDate(context.lastChargedAt),
          inProgressSince: shiftDate(context.inProgressSince),
          pausedAt: shiftDate(context.pausedAt),
          failedAt: shiftDate(context.failedAt),
          completedAt: shiftDate(context.completedAt),
          deletedAt: shiftDate(context.deletedAt),
          uncollectibleAt: shiftDate(context.uncollectibleAt),
        };
      }),
      storeInProgressSubState: assign(({ self }) => {
        const snapshot = self.getSnapshot();
        let inProgressSubState = null;
        if (snapshot.matches('inProgress.readyToCharge')) {
          inProgressSubState = 'readyToCharge';
        } else if (snapshot.matches('inProgress.charging')) {
          inProgressSubState = 'charging';
        } else if (snapshot.matches('inProgress.waitingForPrescription')) {
          inProgressSubState = 'waitingForPrescription';
        } else if (snapshot.matches('inProgress.waitingBetweenRefills')) {
          inProgressSubState = 'waitingBetweenRefills';
        }
        return {
          inProgressSubState,
        };
      }),
      clearInProgressSubState: assign({ inProgressSubState: null }),
      sendNotification: ({ context }) => {
        const nextEvent = context.notificationSent.nextRefill
          ? 'nextDose'
          : 'nextRefill';
        emitEvent({ event: `treatment_notify_${nextEvent}` });
      },
      updateNotificationStatus: assign({
        notificationSent: ({ context }) => ({
          ...context.notificationSent,
          [context.notificationSent.nextRefill ? 'nextDose' : 'nextRefill']:
            true,
        }),
      }),
      updateNextNotificationIn: assign({
        nextNotificationIn: ({ context }) => {
          const { nextDose, nextRefill } = context.notificationSent ?? {};
          let nextNotificationIn: string;
          if (!nextRefill && !nextDose) {
            nextNotificationIn = dayjs(context.nextRefillDate)
              .subtract(daysLeftForNotification.nextRefill, 'days')
              .toISOString();
          } else if (nextRefill && !nextDose) {
            nextNotificationIn = dayjs(context.nextRefillDate)
              .subtract(daysLeftForNotification.nextDose, 'days')
              .toISOString();
          } else if (nextRefill && nextDose) {
            nextNotificationIn = null;
          }
          return nextNotificationIn;
        },
      }),
      resetNotificationStatus: assign({
        notificationSent: { nextDose: false, nextRefill: false },
      }),
      transfer: assign(({ context, event }) => {
        if (event.type !== 'transfer') return context;
        const refills = context.currentRefill;
        const products = context.products.slice(0, context.currentRefill + 1);
        const remainingDays = products
          .slice(refills)
          .reduce((sum, product) => sum + product.days, 0);

        const endOfLastRefillDate = dayjs()
          .add(remainingDays, 'day')
          .toISOString();

        if (context.notificationSent.nextRefill) {
          emitEvent({ event: 'treatment_fix_nextRefill' });
        }

        return {
          transferredTo: event.treatmentId,
          products,
          refills,
          endOfLastRefillDate,
        };
      }),
    },
  }).createMachine({
    context: (ctx) => {
      const input = ctx.input as TreatmentMachineInput;

      if (input.productPricesList.length > 0) {
        input.productPricesList[0] = {
          ...input.productPricesList[0],
          startDate: input.delayUntil ? null : dayjs().toISOString(),
        };
      }

      const defaultContext = {
        patientId: input.patientId,
        treatmentId: input.treatmentId,
        pharmacy: input.pharmacy,
        vials: input.vials,
        refillSystem: input.refillSystem,
        activeProduct: input.productPricesList[0] as ActiveTreatmentProduct,
        isCore: input.isCore,
        currentRefill: 0,
        refills: input.refills,
        nextEventIn: null,
        nextNotificationIn: null,
        notificationSent: { nextDose: false, nextRefill: false },
        nextRefillDate: null,
        endOfLastRefillDate: null,
        products: input.productPricesList,
        delayUntil: input.delayUntil,
        inProgressSince: null,
        lastChargedAt: null,
        failedAt: null,
        completedAt: null,
        cancelledAt: null,
        deletedAt: null,
        uncollectibleAt: null,
        pausedAt: null,
      };

      return {
        ...defaultContext,
        ...persistedContext,
      };
    },
    id: 'Treatment',
    initial: 'scheduled',
    on: {
      notify: {
        guard: 'isTimeToNotify',
        actions: enqueueActions(({ enqueue }) => {
          enqueue('sendNotification');
          enqueue('updateNotificationStatus');
          enqueue('updateNextNotificationIn');
        }),
      },
      transfer: {
        guard: and([
          not('isTransferred'),
          not(
            or([
              stateIn('completed'),
              stateIn('uncollectible'),
              stateIn('transferred'),
            ]),
          ),
        ]),
        actions: 'transfer',
      },
      // fixNextRefill: {
      //   guard: 'isTransferred',
      //   actions: 'fixNextRefill',
      // },
      shiftDateForward: {
        guard: and([
          'canShiftDateForward',
          not(
            or([
              stateIn('completed'),
              stateIn('uncollectible'),
              stateIn('cancelled'),
              stateIn('failed'),
            ]),
          ),
        ]),
        actions: 'shiftDateForward',
      },
    },
    states: {
      scheduled: {
        entry: 'init',
        always: [
          {
            target: 'inProgress',
            guard: 'canStart',
          },
        ],
        on: {
          next: [
            {
              target: 'inProgress',
              guard: 'canStart',
            },
          ],
          start: { target: 'inProgress' },
          pause: { target: 'paused' },
          cancel: { target: 'cancelled' },
        },
      },
      inProgress: {
        initial: 'chooseSubState',
        on: {
          cancel: { target: '#Treatment.cancelled' },
          uncollectible: { target: 'uncollectible' },
        },
        states: {
          chooseSubState: {
            always: [
              {
                target: 'readyToCharge',
                guard: ({ context }) =>
                  context.inProgressSubState === 'readyToCharge' ||
                  !context.inProgressSubState,
              },
              {
                target: 'charging',
                guard: ({ context }) =>
                  context.inProgressSubState === 'charging',
              },
              {
                target: 'waitingForPrescription',
                guard: ({ context }) =>
                  context.inProgressSubState === 'waitingForPrescription',
              },
              {
                target: 'waitingBetweenRefills',
                actions: 'updateNextEventInForWaitingBetweenRefills',
                guard: ({ context }) =>
                  context.inProgressSubState === 'waitingBetweenRefills',
              },
            ],
          },
          readyToCharge: {
            entry: enqueueActions(({ enqueue }) => {
              enqueue('setActiveProduct');
              enqueue('setRefillDates');
              enqueue('updateNextEventInForCharging');
              enqueue('resetNotificationStatus');
              enqueue('clearInProgressSubState');
            }),
            on: {
              next: [
                {
                  target: 'charging',
                  guard: 'timeToCharge',
                },
                { target: 'readyToCharge' },
              ],
            },
          },
          charging: {
            entry: [
              'removeNextEventIn',
              'clearInProgressSubState',
              'runCharge',
            ],
            on: {
              chargeSucceeded: [
                {
                  target: 'prescribing',
                  guard: 'hasExternalMapping',
                  actions: 'captureExternalMapping',
                },
                {
                  target: 'waitingForPrescription',
                  actions: 'captureExternalMapping',
                },
              ],
              chargeFailed: { target: '#Treatment.failed' },
            },
          },
          prescribing: {
            entry: [
              'removeNextEventIn',
              'clearInProgressSubState',
              'runPrescribe',
            ],
            on: {
              prescribeSucceeded: { target: 'waitingBetweenRefills' },
              prescribeFailed: { target: 'waitingForPrescription' },
            },
          },
          waitingForPrescription: {
            entry: [
              'removeNextEventIn',
              'storeLastChargedAt',
              'clearInProgressSubState',
            ],
            on: {
              prescribed: {
                target: 'waitingBetweenRefills',
              },
              pause: {
                target: '#Treatment.paused',
              },
            },
          },
          waitingBetweenRefills: {
            entry: [
              'clearInProgressSubState',
              'updateNextEventInForWaitingBetweenRefills',
              'updateNextNotificationIn',
            ],
            on: {
              fireNext: {
                guard: and(['hasMoreRefills', not('isTransferred')]),
                target: 'readyToCharge',
                actions: ['endActiveProduct', 'incrementRefillCounter'],
              },
              moveRefillDate: {
                guard: and([
                  'hasMoreRefills',
                  'isValidDateToMoveRefill',
                  not('isTransferred'),
                ]),
                actions: 'moveRefillDate',
              },
              next: [
                {
                  guard: and(['timeForNextRefill', 'isTransferred']),
                  actions: 'resetNotificationStatus',
                  target: '#Treatment.transferred',
                },
                {
                  guard: and(['timeForNextRefill', 'hasMoreRefills']),
                  target: 'readyToCharge',
                  actions: ['endActiveProduct', 'incrementRefillCounter'],
                },
                {
                  guard: and(['timeForNextRefill', not('hasMoreRefills')]),
                  actions: 'resetNotificationStatus',
                  target: '#Treatment.completed',
                },
                { target: 'waitingBetweenRefills' },
              ],
              prescribeFailed: { target: 'waitingForPrescription' },
              pause: { target: '#Treatment.paused' },
            },
          },
        },
      },
      paused: {
        entry: [
          'recordPauseStart',
          'storeInProgressSubState',
          'removeNextNotificationIn',
        ],
        exit: 'recordPauseEnd',
        on: {
          resume: { target: 'inProgress' },
          next: { guard: 'canNext', target: 'inProgress' },
          cancel: { target: 'cancelled' },
          uncollectible: { target: 'uncollectible' },
        },
      },
      failed: {
        entry: ['storeFailedAt', 'removeNextEventIn'],
        exit: assign({ failedAt: null }),
        on: {
          retry: {
            target: 'inProgress.charging',
          },
          chargeSucceeded: {
            target: 'inProgress.waitingForPrescription',
          },
          cancel: { target: 'cancelled' },
          uncollectible: { target: 'uncollectible' },
        },
      },
      cancelled: {
        entry: [
          enqueueActions(({ context, self, enqueue }) => {
            const snapshot = self.getSnapshot();
            const shouldDelete =
              snapshot.matches('inProgress.readyToCharge') &&
              context.currentRefill === 0;

            enqueue('removeNextEventIn');
            enqueue('removeNextNotificationIn');
            if (shouldDelete) {
              enqueue('storeDeletedAt');
              enqueue('emitCancelledEvent');
              enqueue('emitDeleteEvent');
            } else {
              enqueue('storeCancelledAt');
              enqueue('emitCancelledEvent');
            }
          }),
        ],
        on: {
          uncollectible: { target: 'uncollectible' },
        },
      },
      completed: {
        type: 'final',
        entry: [
          'storeCompletedAt',
          'emitCompletedEvent',
          'removeNextEventIn',
          'removeNextNotificationIn',
          'endActiveProduct',
        ],
      },
      uncollectible: {
        type: 'final',
        entry: [
          'storeUncollectibleAt',
          'removeNextEventIn',
          'removeNextNotificationIn',
          'endActiveProduct',
        ],
      },
      transferred: {
        type: 'final',
        entry: [
          'removeNextEventIn',
          'removeNextNotificationIn',
          'endActiveProduct',
        ],
      },
    },
  });
};
